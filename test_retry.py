#!/usr/bin/env python3
"""
测试增强重试机制的脚本
"""
import sys
import os
sys.path.append('.')

# 导入重试函数
from Zimbra_SOAP_API_Manage import robust_http_request, safe_post_request, safe_get_request, create_robust_session
import requests

def test_robust_retry_mechanism():
    """测试增强的重试机制"""
    print("[*] Testing enhanced retry mechanism...")

    # 测试1: 不存在的域名
    test_url = "https://nonexistent-domain-12345.com/test"

    print(f"\n[*] Test 1: Testing GET request to non-existent domain: {test_url}")
    response = robust_http_request(None, test_url, {}, None, "Test Non-existent Domain", max_retries=2, timeout=5, method="GET")
    if response is None:
        print("[+] Expected result: Request failed after retries")
    else:
        print(f"[-] Unexpected success: {response.status_code}")

    # 测试2: 真实但可能较慢的端点
    print("\n[*] Test 2: Testing with a real endpoint...")
    try:
        response = robust_http_request(None, "https://httpbin.org/status/200", {}, None, "Test Real Endpoint", max_retries=2, timeout=10, method="GET")
        if response and response.status_code == 200:
            print(f"[+] Success: {response.status_code}")
        else:
            print(f"[-] Failed or unexpected status: {response.status_code if response else 'None'}")
    except Exception as e:
        print(f"[-] Exception: {e}")

    # 测试3: 测试HTTP错误状态码重试
    print("\n[*] Test 3: Testing HTTP 500 error retry...")
    try:
        response = robust_http_request(None, "https://httpbin.org/status/500", {}, None, "Test HTTP 500", max_retries=2, timeout=10, method="GET")
        if response:
            print(f"[+] Got response with status: {response.status_code}")
        else:
            print("[-] No response received")
    except Exception as e:
        print(f"[-] Exception: {e}")

    # 测试4: 测试会话重试
    print("\n[*] Test 4: Testing session-based requests...")
    try:
        session = create_robust_session()
        response = robust_http_request(session, "https://httpbin.org/get", {}, None, "Test Session Request", max_retries=2, timeout=10, method="GET")
        if response and response.status_code == 200:
            print(f"[+] Session request success: {response.status_code}")
        else:
            print(f"[-] Session request failed: {response.status_code if response else 'None'}")
    except Exception as e:
        print(f"[-] Session test exception: {e}")

    # 测试5: 测试POST请求
    print("\n[*] Test 5: Testing POST request...")
    try:
        test_data = '{"test": "data"}'
        headers = {"Content-Type": "application/json"}
        response = robust_http_request(None, "https://httpbin.org/post", headers, test_data, "Test POST Request", max_retries=2, timeout=10, method="POST")
        if response and response.status_code == 200:
            print(f"[+] POST request success: {response.status_code}")
        else:
            print(f"[-] POST request failed: {response.status_code if response else 'None'}")
    except Exception as e:
        print(f"[-] POST test exception: {e}")

def test_legacy_functions():
    """测试传统的安全请求函数"""
    print("\n[*] Testing legacy safe request functions...")

    try:
        print("[*] Testing safe_get_request...")
        response = safe_get_request("https://httpbin.org/get", timeout=10, max_retries=2, operation_name="Legacy GET Test")
        if response and response.status_code == 200:
            print(f"[+] Legacy GET success: {response.status_code}")
        else:
            print(f"[-] Legacy GET failed: {response.status_code if response else 'None'}")
    except Exception as e:
        print(f"[-] Legacy GET exception: {e}")

    try:
        print("[*] Testing safe_post_request...")
        test_data = '{"test": "legacy"}'
        headers = {"Content-Type": "application/json"}
        response = safe_post_request("https://httpbin.org/post", headers=headers, data=test_data, timeout=10, max_retries=2, operation_name="Legacy POST Test")
        if response and response.status_code == 200:
            print(f"[+] Legacy POST success: {response.status_code}")
        else:
            print(f"[-] Legacy POST failed: {response.status_code if response else 'None'}")
    except Exception as e:
        print(f"[-] Legacy POST exception: {e}")

if __name__ == "__main__":
    test_robust_retry_mechanism()
    test_legacy_functions()
    print("\n[*] All tests completed!")
