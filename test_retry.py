#!/usr/bin/env python3
"""
测试重试机制的简单脚本
"""
import sys
import os
sys.path.append('.')

# 导入重试函数
from Zimbra_SOAP_API_Manage import retry_request, safe_post_request, safe_get_request
import requests

def test_retry_mechanism():
    """测试重试机制"""
    print("[*] Testing retry mechanism...")
    
    # 测试一个不存在的URL
    test_url = "https://nonexistent-domain-12345.com/test"
    
    try:
        print(f"[*] Testing GET request to: {test_url}")
        response = safe_get_request(test_url, timeout=5, max_retries=3)
        print(f"[+] Unexpected success: {response.status_code}")
    except Exception as e:
        print(f"[+] Expected failure after retries: {e}")
    
    print("\n[*] Testing with a real but slow endpoint...")
    try:
        # 使用一个真实但可能较慢的端点
        response = safe_get_request("https://httpbin.org/delay/1", timeout=10, max_retries=2)
        print(f"[+] Success: {response.status_code}")
    except Exception as e:
        print(f"[-] Failed: {e}")

if __name__ == "__main__":
    test_retry_mechanism()
